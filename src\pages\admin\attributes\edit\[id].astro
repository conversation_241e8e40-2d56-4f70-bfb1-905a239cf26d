---
import AdminLayout from '../../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../../utils/auth';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Получение параметров из URL
const { id } = Astro.params;
const url = new URL(Astro.request.url);
const attributeType = url.searchParams.get('type');

if (!id || !attributeType) {
  return Astro.redirect('/admin/attributes');
}

// Загрузка данных об атрибутах
import attributesFile from '../../../../../data/product/attributes.json';
const attributesData = attributesFile;

// Поиск атрибута по ID и типу
let attribute = null;
let attributeIndex = -1;

if (attributesData[attributeType]) {
  if (attributeType === 'standard_sizes') {
    // Для размеров ищем по всем типам
    for (const [sizeType, sizes] of Object.entries(attributesData[attributeType])) {
      const index = sizes.findIndex((size, idx) => `${sizeType}_${idx}` === id);
      if (index !== -1) {
        attribute = { ...sizes[index], sizeType, sizeIndex: index };
        attributeIndex = index;
        break;
      }
    }
  } else if (Array.isArray(attributesData[attributeType])) {
    if (attributesData[attributeType].length > 0 && typeof attributesData[attributeType][0] === 'string') {
      // Для простых строковых массивов (textures, сезонность и т.д.) ID это само значение
      attributeIndex = attributesData[attributeType].findIndex(item => item === id);
      if (attributeIndex !== -1) {
        attribute = attributesData[attributeType][attributeIndex];
      }
    } else if (attributesData[attributeType].length > 0 && typeof attributesData[attributeType][0] === 'object') {
      // Для объектов ищем по полю id или class
      attributeIndex = attributesData[attributeType].findIndex(item => {
        const itemId = item.id || item.class;
        return itemId === id;
      });
      if (attributeIndex !== -1) {
        attribute = attributesData[attributeType][attributeIndex];
      }
    }
  }
}

// Если атрибут не найден, перенаправляем на список
if (!attribute) {
  return Astro.redirect('/admin/attributes');
}

// Определяем типы атрибутов и их отображаемые названия
const predefinedAttributeTypes = {
  colors: 'Цвет',
  textures: 'Текстура',
  strength_classes: 'Класс прочности',
  frost_resistance: 'Морозостойкость',
  water_absorption: 'Водопоглощение',
  standard_sizes: 'Размер',
  surfaces: 'Поверхность',
  patterns: 'Рисунок',
  color_pigments: 'Цветовые пигменты'
};

// Функция для получения всех типов атрибутов (предопределенные + пользовательские)
function getAllAttributeTypes(data) {
  const allTypes = { ...predefinedAttributeTypes };

  // Добавляем пользовательские типы из данных
  Object.keys(data).forEach(key => {
    if (!predefinedAttributeTypes[key]) {
      // Преобразуем ключ в читаемое название
      allTypes[key] = key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ');
    }
  });

  return allTypes;
}

const allAttributeTypes = getAllAttributeTypes(attributesData);
const attributeTypeName = allAttributeTypes[attributeType] || attributeType;
---

<AdminLayout title={`Редактирование атрибута | LuxBeton`}>
  <div class="container mx-auto py-8 px-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">Редактирование атрибута</h1>
      <a href="/admin/attributes" class="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400 inline-flex items-center">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
        </svg>
        Назад к списку атрибутов
      </a>
    </div>

    <form id="attribute-form" class="bg-white rounded-lg shadow-md p-6">
      <input type="hidden" id="attribute-id" value={id}>
      <input type="hidden" id="attribute-type" value={attributeType}>
      <input type="hidden" id="attribute-index" value={attributeIndex}>

      <!-- Информация о типе атрибута -->
      <div class="mb-6">
        <h2 class="text-xl font-semibold mb-4">Тип атрибута</h2>
        <div class="bg-gray-50 p-4 rounded-md">
          <p class="text-sm text-gray-600">Редактируется атрибут типа: <span class="font-medium">{attributeTypeName}</span></p>
        </div>
      </div>

      <!-- Динамические поля для атрибута -->
      <div class="mb-6">
        <h2 class="text-xl font-semibold mb-4">Данные атрибута</h2>
        <div id="dynamic-fields">
          <!-- Поля будут сгенерированы через JavaScript -->
        </div>
      </div>

      <div class="flex justify-end space-x-2">
        <a href="/admin/attributes" class="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400">
          Отмена
        </a>
        <button
          type="submit"
          class="save-attribute-btn text-white px-4 py-2 rounded"
          style="background-color: #3b82f6;"
        >
          Сохранить изменения
        </button>
      </div>
    </form>
  </div>
</AdminLayout>

<style>
  /* Стили для кнопки сохранения атрибута */
  .save-attribute-btn:hover {
    background-color: #2563eb !important;
  }
</style>

<script define:vars={{ attribute, attributeType, id, attributeIndex }}>
  // Сохраняем данные атрибута в глобальной переменной
  window.currentAttribute = attribute;
  window.currentAttributeType = attributeType;
  window.currentAttributeId = id;
  window.currentAttributeIndex = attributeIndex;

  const dynamicFields = document.getElementById('dynamic-fields');
  const attributeForm = document.getElementById('attribute-form');

  // Генерация полей формы на основе типа атрибута
  function generateEditFields() {
    const type = window.currentAttributeType;
    const data = window.currentAttribute;

    if (type === 'colors') {
      dynamicFields.innerHTML = `
        <div class="mb-4">
          <label for="color-id" class="block text-sm font-medium text-gray-700 mb-1">ID</label>
          <input type="text" id="color-id" name="color-id" value="${data.id}"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
        </div>
        <div class="mb-4">
          <label for="color-name" class="block text-sm font-medium text-gray-700 mb-1">Название</label>
          <input type="text" id="color-name" name="color-name" value="${data.name}"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
        </div>
        <div class="mb-4">
          <label for="color-hex" class="block text-sm font-medium text-gray-700 mb-1">Hex код</label>
          <input type="color" id="color-hex" name="color-hex" value="${data.hex}"
                 class="w-full h-10 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
        </div>
      `;
    } else if (type === 'textures') {
      dynamicFields.innerHTML = `
        <div class="mb-4">
          <label for="texture-name" class="block text-sm font-medium text-gray-700 mb-1">Название текстуры</label>
          <input type="text" id="texture-name" name="texture-name" value="${data}"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
        </div>
      `;
    } else if (type === 'sizes') {
      dynamicFields.innerHTML = `
        <div class="mb-4">
          <label for="size-type" class="block text-sm font-medium text-gray-700 mb-1">Тип изделия</label>
          <select id="size-type" name="size-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
            <option value="pavers" ${data.sizeType === 'pavers' ? 'selected' : ''}>Брусчатка (pavers)</option>
            <option value="curbs" ${data.sizeType === 'curbs' ? 'selected' : ''}>Бордюры (curbs)</option>
            <option value="drainage" ${data.sizeType === 'drainage' ? 'selected' : ''}>Дренаж (drainage)</option>
          </select>
        </div>
        <div class="mb-4">
          <label for="size-length" class="block text-sm font-medium text-gray-700 mb-1">Длина (мм)</label>
          <input type="number" id="size-length" name="size-length" value="${data.length}"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
        </div>
        <div class="mb-4">
          <label for="size-width" class="block text-sm font-medium text-gray-700 mb-1">Ширина (мм)</label>
          <input type="number" id="size-width" name="size-width" value="${data.width}"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
        </div>
        <div class="mb-4">
          <label for="size-height" class="block text-sm font-medium text-gray-700 mb-1">Высота (мм)</label>
          <input type="number" id="size-height" name="size-height" value="${data.height}"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
        </div>
      `;
    } else if (['strength_classes', 'frost_resistance', 'water_absorption'].includes(type)) {
      dynamicFields.innerHTML = `
        <div class="mb-4">
          <label for="class-name" class="block text-sm font-medium text-gray-700 mb-1">Класс</label>
          <input type="text" id="class-name" name="class-name" value="${data.class}"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
        </div>
        <div class="mb-4">
          <label for="class-description" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
          <textarea id="class-description" name="class-description" rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>${data.description}</textarea>
        </div>
      `;
    } else if (['surfaces', 'patterns', 'color_pigments'].includes(type)) {
      dynamicFields.innerHTML = `
        <div class="mb-4">
          <label for="item-id" class="block text-sm font-medium text-gray-700 mb-1">ID</label>
          <input type="text" id="item-id" name="item-id" value="${data.id}"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
        </div>
        <div class="mb-4">
          <label for="item-name" class="block text-sm font-medium text-gray-700 mb-1">Название</label>
          <input type="text" id="item-name" name="item-name" value="${data.name}"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
        </div>
        <div class="mb-4">
          <label for="item-description" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
          <textarea id="item-description" name="item-description" rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>${data.description}</textarea>
        </div>
      `;
    } else if (typeof data === 'string') {
      // Для простых строковых значений (textures, сезонность и т.д.)
      dynamicFields.innerHTML = `
        <div class="mb-4">
          <label for="simple-value" class="block text-sm font-medium text-gray-700 mb-1">Значение</label>
          <input type="text" id="simple-value" name="simple-value" value="${data}"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
        </div>
      `;
    } else {
      // Для других типов атрибутов определяем структуру на основе данных
      if (typeof data === 'object' && data !== null) {
          // Объектная структура
          dynamicFields.innerHTML = `
            <div class="mb-4">
              <label for="object-id" class="block text-sm font-medium text-gray-700 mb-1">ID</label>
              <input type="text" id="object-id" name="object-id" value="${data.id || ''}"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
            </div>
            <div class="mb-4">
              <label for="object-name" class="block text-sm font-medium text-gray-700 mb-1">Название</label>
              <input type="text" id="object-name" name="object-name" value="${data.name || ''}"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
            </div>
            <div class="mb-4">
              <label for="object-description" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
              <textarea id="object-description" name="object-description" rows="3"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">${data.description || ''}</textarea>
            </div>
          `;
      }
    }
  }

  // Обработка отправки формы
  attributeForm.addEventListener('submit', async function(e) {
    e.preventDefault();

    const type = window.currentAttributeType;
    const originalId = window.currentAttributeId;
    let attributeData;

    // Собираем данные в зависимости от типа атрибута
    if (type === 'colors') {
      const id = document.getElementById('color-id').value.trim();
      const name = document.getElementById('color-name').value.trim();
      const hex = document.getElementById('color-hex').value;

      if (!id || !name) {
        await window.adminModal?.showError('Заполните все обязательные поля');
        return;
      }

      attributeData = { id, name, hex };
    } else if (type === 'textures') {
      const name = document.getElementById('texture-name').value.trim();
      if (!name) {
        await window.adminModal?.showError('Введите название текстуры');
        return;
      }
      attributeData = name;
    } else if (type === 'sizes') {
      const sizeType = document.getElementById('size-type').value;
      const length = parseInt(document.getElementById('size-length').value);
      const width = parseInt(document.getElementById('size-width').value);
      const height = parseInt(document.getElementById('size-height').value);

      if (!sizeType || !length || !width || !height) {
        await window.adminModal?.showError('Заполните все поля размера');
        return;
      }

      attributeData = {
        sizeType: sizeType,
        sizeIndex: window.currentAttributeIndex,
        sizeData: { length, width, height }
      };
    } else if (['strength_classes', 'frost_resistance', 'water_absorption'].includes(type)) {
      const className = document.getElementById('class-name').value.trim();
      const description = document.getElementById('class-description').value.trim();

      if (!className || !description) {
        await window.adminModal?.showError('Заполните все поля');
        return;
      }

      attributeData = { class: className, description };
    } else if (['surfaces', 'patterns', 'color_pigments'].includes(type)) {
      const id = document.getElementById('item-id').value.trim();
      const name = document.getElementById('item-name').value.trim();
      const description = document.getElementById('item-description').value.trim();

      if (!id || !name || !description) {
        await window.adminModal?.showError('Заполните все поля');
        return;
      }

      attributeData = { id, name, description };
    } else {
      // Проверяем, есть ли поле для простого значения
      const simpleValue = document.getElementById('simple-value');
      if (simpleValue) {
        // Простая структура (строки)
        const value = simpleValue.value.trim();
        if (!value) {
          await window.adminModal?.showError('Введите значение атрибута');
          return;
        }
        attributeData = value;
      } else {
        // Для других типов атрибутов
        const objectId = document.getElementById('object-id');
        if (objectId) {
          // Объектная структура
          const id = objectId.value.trim();
          const name = document.getElementById('object-name').value.trim();
          const description = document.getElementById('object-description').value.trim();

          if (!id || !name) {
            await window.adminModal?.showError('Заполните обязательные поля (ID и название)');
            return;
          }

          attributeData = { id, name, description };
        }
      }
    }

    try {
      const response = await fetch('/api/admin/attributes', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          attributeType: type,
          attributeData,
          originalId: originalId
        })
      });

      if (response.ok) {
        await window.adminModal?.showSuccess('Атрибут успешно обновлен');
        // Перенаправляем обратно на страницу атрибутов с сохранением текущей вкладки
        window.location.href = `/admin/attributes?tab=${encodeURIComponent(type)}`;
      } else {
        const error = await response.json();
        await window.adminModal?.showError('Ошибка при обновлении: ' + (error.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      console.error('Ошибка:', error);
      await window.adminModal?.showError('Ошибка при обновлении атрибута');
    }
  });

  // Инициализация формы
  document.addEventListener('DOMContentLoaded', function() {
    generateEditFields();
  });
</script>
