// Форматирование цены с учетом символа валюты (simvol) и формата
// amount: число, simvol: строка, format: строка (например, '{simvol}{amount}' или '{amount} {simvol}')
// decimalSeparator: строка, thousandsSeparator: строка
export default function formatPrice({
  amount,
  simvol,
  format = '{amount} {simvol}',
  decimalSeparator = '.',
  thousandsSeparator = ',',
  decimals = 2
}) {
  if (typeof amount !== 'number') amount = parseFloat(amount) || 0;
  let parts = amount.toFixed(decimals).split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
  let formattedAmount = parts.join(decimalSeparator);
  return format
    .replace('{simvol}', simvol || '')
    .replace('{amount}', formattedAmount);
} 