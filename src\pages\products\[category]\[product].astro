---
import PageLayout from '../../../layouts/PageLayout.astro';
import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';
import type { Product, SettingsProduct } from '../../../types';
import formatUnit from '../../../utils/formatUnit.js';
import formatPrice from '../../../utils/formatPrice.js';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Загружаем все необходимые данные в одном блоке
let attributesData = {};
let attributeTypesConfig = {};
let settingsProduct: SettingsProduct = {} as SettingsProduct;

try {
  const dataPath = path.join(__dirname, '../../../../data/product');

  const attributesFile = await fs.readFile(path.join(dataPath, 'attributes.json'), 'utf-8');
  attributesData = JSON.parse(attributesFile);

  const configPath = path.join(dataPath, 'attribute-types-config.json');
  const configFile = await fs.readFile(configPath, 'utf-8');
  attributeTypesConfig = JSON.parse(configFile);

  const settingsPath = path.join(dataPath, 'settings-product.json');
  const settingsFile = await fs.readFile(settingsPath, 'utf-8');
  settingsProduct = JSON.parse(settingsFile);

} catch (error) {
  console.error('Ошибка загрузки данных:', error);
}

// Получаем параметры из URL
const categorySlug = Astro.params.category;
const productIdentifier = Astro.params.product; // Может быть как ID, так и SLUG

// Загружаем данные динамически
let product: Product | undefined;

try {
  const productsPath = path.join(__dirname, '../../../../data/product/products.json');
  const productsFile = await fs.readFile(productsPath, 'utf-8');
  const productsData: Product[] = JSON.parse(productsFile);

  // Находим товар по SLUG (приоритет) или ID (для обратной совместимости) и категории
  product = productsData.find(p => {
    const matchesCategory = p.categorySlug === categorySlug;
    const matchesSlug = p.slug === productIdentifier;
    const matchesId = p.id === productIdentifier;

    // Приоритет SLUG, затем ID для обратной совместимости
    return matchesCategory && (matchesSlug || (!p.slug && matchesId) || matchesId);
  });
} catch (error) {
  console.error('Ошибка загрузки данных товара:', error);
  return Astro.redirect('/404');
}

if (!product) {
  return Astro.redirect('/404');
}

// Combine main image with additional images
const allImages = [product.images.main, ...product.images.additional];

// Функция для получения отображаемого значения атрибута
function getAttributeDisplayValue(attributeType: string, attributeValue: any, config: any) {
  if (!attributeValue) return null;

  // Универсальная обработка для объектов с полем name (приоритет)
  if (typeof attributeValue === 'object' && !Array.isArray(attributeValue) && attributeValue.name) {
    return attributeValue.name;
  }

  // Специальная обработка для некоторых типов атрибутов
  switch (attributeType) {
    case 'weight':
      if (typeof attributeValue === 'object' && attributeValue.value && attributeValue.unit) {
        return `${attributeValue.value} ${attributeValue.unit}`;
      }
      if (typeof attributeValue === 'number') {
        return `${attributeValue} кг`;
      }
      break;

    case 'size':
      if (typeof attributeValue === 'object' && attributeValue.length && attributeValue.width && attributeValue.height) {
        // Пропускаем размеры с нулевыми значениями
        if (attributeValue.length === 0 || attributeValue.width === 0 || attributeValue.height === 0) {
          return null;
        }
        return `${attributeValue.length}×${attributeValue.width}×${attributeValue.height} мм`;
      }
      break;

    case 'texture':
    case 'surface':
    case 'pattern':
    case 'strength':
      // Простые строковые атрибуты
      if (typeof attributeValue === 'string' && attributeValue.trim()) {
        return attributeValue;
      }
      break;
  }

  // Если нет конфигурации, возвращаем простое значение
  if (!config) {
    if (Array.isArray(attributeValue)) {
      return attributeValue.join(', ');
    }
    if (typeof attributeValue === 'object') {
      return JSON.stringify(attributeValue);
    }
    return String(attributeValue);
  }

  // Для простых массивов (isSimpleArray: true)
  if (config.isSimpleArray) {
    if (Array.isArray(attributeValue)) {
      return attributeValue.join(', ');
    }
    return String(attributeValue);
  }

  // Для объектов с полями
  if (typeof attributeValue === 'object' && !Array.isArray(attributeValue)) {
    // Если есть конфигурация и формат
    if (config && config.display && config.display.format && config.display.format.trim()) {
      let formatted = config.display.format;
      if (config.fields) {
        config.fields.forEach(field => {
          const value = attributeValue[field.key];
          if (value !== undefined) {
            formatted = formatted.replace(`{${field.key}}`, value);
          }
        });
      }
      return formatted;
    }

    // Универсальная обработка объектов
    let displayFields = [];

    if (config) {
      // Если есть listView и он не пустой
      if (config.display?.listView && config.display.listView.length > 0) {
        displayFields = config.display.listView;
      }
      // Если есть поля в конфигурации
      else if (config.fields && config.fields.length > 0) {
        displayFields = config.fields.map(f => f.key);
      }
    }

    // Если нет конфигурации или полей, используем умные значения по умолчанию
    if (displayFields.length === 0) {
      // Приоритетные поля для отображения
      const priorityFields = ['name', 'title', 'label', 'value', 'description'];
      const availableFields = Object.keys(attributeValue);

      // Ищем приоритетные поля
      for (const field of priorityFields) {
        if (availableFields.includes(field) && attributeValue[field]) {
          return String(attributeValue[field]);
        }
      }

      // Если нет приоритетных полей, используем все доступные
      displayFields = availableFields;
    }

    const values = displayFields
      .map(field => attributeValue[field])
      .filter(val => val !== undefined && val !== '' && val !== null);

    return values.join(' ');
  }

  // Для массивов объектов
  if (Array.isArray(attributeValue)) {
    return attributeValue.map(item => {
      if (typeof item === 'object') {
        if (config.display && config.display.format) {
          let formatted = config.display.format;
          if (config.fields) {
            config.fields.forEach(field => {
              const value = item[field.key];
              if (value !== undefined) {
                formatted = formatted.replace(`{${field.key}}`, value);
              }
            });
          }
          return formatted;
        } else {
          const displayFields = config.display?.listView || (config.fields ? config.fields.map(f => f.key) : Object.keys(item));
          return displayFields
            .map(field => item[field])
            .filter(val => val !== undefined)
            .join(' ');
        }
      }
      return String(item);
    }).join(', ');
  }

  return String(attributeValue);
}

// Функция для получения названия атрибута
function getAttributeName(attributeType, config) {
  return config?.name || attributeType;
}

// Интерактивные атрибуты (размеры, цвета) - исключаем из общего списка
const interactiveAttributes = ['size', 'colors'];

// Получаем все атрибуты продукта для динамического отображения (исключая интерактивные)
const productAttributes = Object.entries(product.attributes || {})
  .filter(([key]) => !interactiveAttributes.includes(key))
  .map(([key, value]) => {
    const config = attributeTypesConfig[key];

    // Проверяем настройку отображения на странице товара
    if (config && config.showOnProductPage === false) {
      return null;
    }

    const displayValue = getAttributeDisplayValue(key, value, config);

    // Пропускаем пустые значения и значения "0"
    if (!displayValue || displayValue === '0' || displayValue === '0×0×0 мм') return null;

    return {
      key,
      name: getAttributeName(key, config),
      value: displayValue,
      config
    };
  })
  .filter(attr => attr !== null);



---

<PageLayout
  title={product.name}
  pageTitle={product.name.toUpperCase()}
  breadcrumbs={[
    { text: 'Продукция', url: '/products' },
    { text: product.category, url: `/products/${product.categorySlug}` },
    { text: product.name, url: `/products/${product.categorySlug}/${product.slug}` }
  ]}
>
  <section class="py-16">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-10 items-start lg:flex lg:justify-between">
        <!-- Product Image Gallery -->
        <div class="product-gallery lg:w-[47.5%]" data-all-images={JSON.stringify(allImages)}>
          <div class="relative">
            <img
              id="mainImage"
              src={`/product/${allImages[0]}`}
              alt={product.name}
              class="w-full aspect-square object-cover rounded-none shadow-md mb-4"
            />
            <!-- Navigation Dots -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
              {allImages.map((_, index) => (
                <button
                  class="w-2 h-2 rounded-full bg-white opacity-50 hover:opacity-100 transition-opacity"
                  data-index={index}
                  aria-label={`Go to image ${index + 1}`}
                ></button>
              ))}
            </div>
          </div>

          <!-- Thumbnails -->
          <div class="grid grid-cols-4 gap-2">
            {allImages.map((img: string, index: number) => (
              <button
                class="relative aspect-square overflow-hidden"
                data-index={index}
                aria-label={`View image ${index + 1}`}
              >
                <img
                  src={`/product/${img}`}
                  alt={`${product.name} - view ${index + 1}`}
                  class="w-full h-full object-cover rounded-none hover:opacity-75 transition-opacity"
                />
              </button>
            ))}
          </div>
        </div>

        <!-- Product Details -->
        <div class="product-details lg:w-[47.5%]">
          <h1 class="text-3xl font-bold mb-4">{product.name}</h1>
          <p class="text-gray-600 mb-4">{product.shortDescription}</p>

          <div class="mb-6">
            <span class="text-2xl font-bold text-primary product-price">
              {formatPrice({
                amount: product.basePrice.value,
                simvol: product.basePrice.simvol || product.basePrice.currency || '₽',
                format: '{amount} {simvol}',
                decimalSeparator: '.',
                thousandsSeparator: ' ',
                decimals: 2
              })}
              {product.basePrice.unit && (
                <span class="text-lg ml-2 text-gray-600">/ {formatUnit(product.basePrice.unit, settingsProduct as any)}</span>
              )}
            </span>
            {product.inStock ? (
              <span class="ml-4 px-3 py-1 bg-green-200 text-green-800 text-sm font-semibold rounded-full">В наличии</span>
            ) : (
              <span class="ml-4 px-3 py-1 bg-red-200 text-red-800 text-sm font-semibold rounded-full">Нет в наличии</span>
            )}
          </div>

        <!-- Product Attributes -->
        <div class="mt-8">
          <h3 class="text-lg font-semibold mb-4">Параметры</h3>
          <div class="space-y-4">
            <!-- Dynamic Parameters (Size) -->
            {product.attributes.size && attributeTypesConfig.sizes?.showOnProductPage === true && (
              <div class="mb-4">
                <span class="block w-full text-gray-600 mb-2">Размер (длинна × ширина × высота):</span>
                <div class="flex flex-wrap gap-2">
                  {product.attributes.size.variants
                    ? product.attributes.size.variants.map((v, index) => (
                        <button
                          type="button"
                          class="size-variant-btn border border-gray-300 px-3 py-2 rounded hover:border-primary transition-colors"
                          data-length={v.length}
                          data-width={v.width}
                          data-height={v.height}
                          data-index={index}
                        >
                          {`${v.length}×${v.width}×${v.height} мм`}
                        </button>
                      ))
                    : <span class="border border-gray-300 px-3 py-2 rounded">
                        {`${product.attributes.size.length}×${product.attributes.size.width}×${product.attributes.size.height} мм`}
                      </span>
                  }
                </div>
              </div>
            )}

            <!-- Dynamic Parameters (Colors) -->
            {product.attributes.colors && product.attributes.colors.length > 0 && attributeTypesConfig.colors?.showOnProductPage === true && (
              <div class="mb-4">
                <span class="block w-full text-gray-600 mb-2">Цвет:</span>
                <div class="flex flex-wrap gap-2">
                  {product.attributes.colors.map((color, index) => (
                    <button
                      type="button"
                      class="color-variant-btn border border-gray-300 px-3 py-2 rounded hover:border-primary transition-colors"
                      data-color={color}
                      data-index={index}
                    >
                      {color}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {productAttributes.length > 0 && (
            <>
              <h3 class="text-lg font-semibold mb-4 mt-8">Особенности</h3>
              <div class="space-y-3">
                {productAttributes.map(attr => (
                  <div class="flex">
                    <span class="w-32 text-gray-600">{attr.name}:</span>
                    <span>{attr.value}</span>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>

          <button class="w-full bg-primary text-white py-3 font-semibold rounded-none hover:bg-primary-dark transition-colors">
            Заказать
          </button>
        </div>
      </div> <!-- This closes the "grid grid-cols-1 lg:grid-cols-2 gap-10 items-start lg:flex lg:justify-between" -->

      <!-- Product Full Description -->
      <div class="product-full-description mt-10 pt-8 border-t border-gray-200">
        <h3 class="text-xl font-semibold mb-3">Описание:</h3>
        <p class="text-gray-700 leading-relaxed mb-6">{product.fullDescription}</p>
      </div>
    </div>
  </section>

  <!-- Zoom Modal -->
  <div id="zoomModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50 hidden">
    <div class="relative bg-white p-2 rounded-none shadow-xl max-w-full max-h-full">
      <img id="zoomedImage" src="" alt="Zoomed product image" class="block max-w-full max-h-[90vh] object-contain"/>
      <button id="closeZoomModal" class="absolute top-4 right-4 w-10 h-10 bg-primary text-white flex items-center justify-center text-2xl leading-none rounded-none hover:bg-primary-dark transition-colors">&times;</button>
    </div>
  </div>
</PageLayout>

<style>
  /* Ensure modal is hidden by default */
  #zoomModal.hidden {
    display: none;
  }
</style>

<script>
  // Image Gallery Functionality
  const mainImageElement = document.getElementById('mainImage') as HTMLImageElement;
  const galleryElement = document.querySelector('.product-gallery') as HTMLElement;

  if (galleryElement && mainImageElement) {
    const allImageUrls: string[] = JSON.parse(galleryElement.dataset.allImages || '[]');

    // Specific selectors for image thumbnails and dot buttons
    const imageThumbnailButtons = galleryElement.querySelectorAll<HTMLButtonElement>('.grid button[data-index]');
    const dotButtons = galleryElement.querySelectorAll<HTMLButtonElement>('.relative .flex button[data-index]');

    let currentIndex = 0;

    function updateMainImageUI(newIndex: number) {
      if (newIndex < 0 || newIndex >= allImageUrls.length) {
        // console.error(`Invalid image index: ${newIndex} for ${allImageUrls.length} images.`);
        return; // Silently return or handle error appropriately
      }

      mainImageElement.src = `/product/${allImageUrls[newIndex]}`;
      currentIndex = newIndex;

      // Update active states for image thumbnails
      imageThumbnailButtons.forEach((thumb) => {
        const thumbIndex = parseInt(thumb.dataset.index!);
        if (thumbIndex === newIndex) {
          thumb.classList.add('ring-2', 'ring-primary');
        } else {
          thumb.classList.remove('ring-2', 'ring-primary');
        }
      });

      // Update active states for dot buttons
      dotButtons.forEach((dot) => {
        const dotIndex = parseInt(dot.dataset.index!);
        if (dotIndex === newIndex) {
          dot.classList.add('opacity-100');
          dot.classList.remove('opacity-50');
        } else {
          dot.classList.add('opacity-50');
          dot.classList.remove('opacity-100');
        }
      });
    }

    // Add click handlers to image thumbnails
    imageThumbnailButtons.forEach((thumb) => {
      thumb.addEventListener('click', () => {
        const newIndex = parseInt(thumb.dataset.index!);
        if (!isNaN(newIndex)) {
          updateMainImageUI(newIndex);
        }
      });
    });

    // Add click handlers to dot buttons
    dotButtons.forEach((dot) => {
      dot.addEventListener('click', () => {
        const newIndex = parseInt(dot.dataset.index!);
        if (!isNaN(newIndex)) {
          updateMainImageUI(newIndex);
        }
      });
    });

    const zoomModal = document.getElementById('zoomModal') as HTMLElement;
    const zoomedImage = document.getElementById('zoomedImage') as HTMLImageElement;
    const closeZoomModalButton = document.getElementById('closeZoomModal') as HTMLButtonElement;

    mainImageElement.addEventListener('click', () => {
      if (zoomedImage && zoomModal && mainImageElement.src) {
        zoomedImage.src = mainImageElement.src;
        zoomModal.classList.remove('hidden');
      }
    });

    closeZoomModalButton?.addEventListener('click', () => {
      zoomModal?.classList.add('hidden');
    });

    zoomModal?.addEventListener('click', (e) => {
      // Close modal if background is clicked (but not the image itself or close button area)
      if (e.target === zoomModal) {
        zoomModal.classList.add('hidden');
      }
    });

    // Swipe functionality for touch devices
    let touchstartX = 0;
    let touchendX = 0;

    function handleSwipe() {
      if (touchendX < touchstartX - 50) { // Swiped left
        if (allImageUrls.length > 0) {
          const nextIndex = (currentIndex + 1) % allImageUrls.length;
          updateMainImageUI(nextIndex);
        }
      }
      if (touchendX > touchstartX + 50) { // Swiped right
        if (allImageUrls.length > 0) {
          const prevIndex = (currentIndex - 1 + allImageUrls.length) % allImageUrls.length;
          updateMainImageUI(prevIndex);
        }
      }
    }

    mainImageElement.addEventListener('touchstart', e => {
      touchstartX = e.changedTouches[0].screenX;
    }, { passive: true });

    mainImageElement.addEventListener('touchend', e => {
      touchendX = e.changedTouches[0].screenX;
      // Check if it's a touch device before handling swipe
      if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
          handleSwipe();
      }
    });

    // Initialize first image if available
    if (allImageUrls.length > 0) {
      updateMainImageUI(0);
    } else {
      // Handle case with no images (e.g., hide gallery or show placeholder)
      // console.warn('No images found for product gallery.');
      mainImageElement.style.display = 'none'; // Hide main image
      const dotsContainer = galleryElement.querySelector('.relative .flex');
      const thumbnailsContainer = galleryElement.querySelector('.grid');
      if(dotsContainer) (dotsContainer as HTMLElement).style.display = 'none';
      if(thumbnailsContainer) (thumbnailsContainer as HTMLElement).style.display = 'none';
    }
  } else {
    // console.error('Product gallery main image or gallery element not found.');
  }

  // Динамическое изменение цены при выборе параметров
  const priceElement = document.querySelector('.product-price');
  const basePrice = parseFloat(priceElement?.textContent?.replace(/[^0-9.,]/g, '').replace(',', '.') || '0');
  const sizeButtons = document.querySelectorAll<HTMLButtonElement>('.size-variant-btn');
  const colorButtons = document.querySelectorAll<HTMLButtonElement>('.color-variant-btn');

  let selectedSize: { length: number; width: number; height: number; index: number } | null = null;
  let selectedColor: { color: string; index: number } | null = null;

  // Коэффициенты изменения цены
  const colorPriceFactors: Record<string, number> = {
    'серый': 1.0,      // без пигментов - базовая цена
    'красный': 1.15,   // 1 пигмент
    'желтый': 1.15,    // 1 пигмент
    'коричневый': 1.15, // 1 пигмент
    'красно/желтый': 1.25, // 2 пигмента
    'синий/зеленый': 1.25, // 2 пигмента
    'уникальное сочетание': 1.4 // 3 пигмента
  };

  // Функция обновления цены
  function updatePrice() {
    if (!priceElement) return;

    let newPrice = basePrice;

    // Применяем коэффициент размера (если выбран)
    if (selectedSize) {
      // Здесь можно добавить логику изменения цены в зависимости от размера
      // Например, увеличение цены для больших размеров
      const volume = selectedSize.length * selectedSize.width * selectedSize.height;
      const baseVolume = 200 * 100 * 40; // базовый объем для примера
      newPrice = newPrice * (volume / baseVolume);
    }

    // Применяем коэффициент цвета (если выбран)
    if (selectedColor && colorPriceFactors[selectedColor.color]) {
      newPrice = newPrice * colorPriceFactors[selectedColor.color];
    }

    // Обновляем отображение цены
    const formattedPrice = newPrice.toFixed(2).replace('.', ',');
    const unit = priceElement.getAttribute('data-unit') || 'шт';
    priceElement.textContent = `${formattedPrice} ₽/${unit}`;
  }

  // Обработчики для кнопок размера
  sizeButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Сбрасываем активное состояние у всех кнопок размера
      sizeButtons.forEach(btn => {
        btn.classList.remove('border-primary', 'bg-primary-50');
        btn.classList.add('border-gray-300');
      });

      // Устанавливаем активное состояние для выбранной кнопки
      button.classList.add('border-primary', 'bg-primary-50');
      button.classList.remove('border-gray-300');

      // Сохраняем выбранный размер
      selectedSize = {
        length: parseInt(button.dataset.length || '0'),
        width: parseInt(button.dataset.width || '0'),
        height: parseInt(button.dataset.height || '0'),
        index: parseInt(button.dataset.index || '0')
      };

      // Обновляем цену
      updatePrice();
    });
  });

  // Обработчики для кнопок цвета
  colorButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Сбрасываем активное состояние у всех кнопок цвета
      colorButtons.forEach(btn => {
        btn.classList.remove('border-primary', 'bg-primary-50');
        btn.classList.add('border-gray-300');
      });

      // Устанавливаем активное состояние для выбранной кнопки
      button.classList.add('border-primary', 'bg-primary-50');
      button.classList.remove('border-gray-300');

      // Сохраняем выбранный цвет
      selectedColor = {
        color: button.dataset.color || '',
        index: parseInt(button.dataset.index || '0')
      };

      // Обновляем цену
      updatePrice();
    });
  });

  // Устанавливаем первый размер и цвет как выбранные по умолчанию, если они есть
  if (sizeButtons.length > 0) {
    sizeButtons[0].click();
  }

  if (colorButtons.length > 0) {
    colorButtons[0].click();
  }
</script>
