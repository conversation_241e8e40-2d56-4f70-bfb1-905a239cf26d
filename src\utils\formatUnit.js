// Форматирование единиц измерения с преобразованием технических ключей в русские названия
// unit: строка (технический ключ единицы измерения)
// settingsData: объект с настройками единиц измерения (опционально)
export default function formatUnit(unit, settingsData = null) {
  if (!unit) return '';
  
  // Если передан settingsData, пытаемся найти единицу в настройках
  if (settingsData && settingsData.units) {
    for (const unitType in settingsData.units) {
      const unitConfig = settingsData.units[unitType];
      if (unitConfig && unitConfig.supported) {
        const foundUnit = unitConfig.supported.find(u => u.key === unit);
        if (foundUnit && foundUnit.label && foundUnit.label.ru) {
          return foundUnit.label.ru;
        }
      }
    }
  }
  
  // Fallback: базовые преобразования для основных единиц
  const unitMap = {
    // Вес
    'kg': 'кг',
    'g': 'г',
    'lb': 'фунт',
    'oz': 'унция',
    
    // Объем
    'L': 'л',
    'mL': 'мл',
    'm3': 'м³',
    'gal': 'галлон',
    
    // Размеры
    'm': 'м',
    'cm': 'см',
    'mm': 'мм',
    'm2': 'м²',
    
    // Штучные
    'piece': 'шт',
    'pack': 'упаковка',
    'set': 'набор',
    
    // Услуги
    'hour': 'час',
    'day': 'день',
    'month': 'месяц',
    'project': 'проект'
  };
  
  return unitMap[unit] || unit;
} 