import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';
import { isAuthenticated } from '../../../utils/auth.js';

// Получаем абсолютный путь к директории проекта
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const configPath = path.join(__dirname, '../../../../data/product/attribute-types-config.json');
const attributesPath = path.join(__dirname, '../../../../data/product/attributes.json');

// Функция для создания конфигурации типа атрибута с разумными значениями по умолчанию
function createDefaultAttributeTypeConfig(name, description = '') {
  // Генерируем ключ из названия
  const key = name.toLowerCase()
    .replace(/[^а-яёa-z0-9\s]/gi, '') // Убираем специальные символы
    .replace(/\s+/g, '_') // Заменяем пробелы на подчеркивания
    .replace(/[а-яё]/g, (char) => { // Транслитерация русских букв
      const translitMap = {
        'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',
        'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
        'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
        'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch',
        'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
      };
      return translitMap[char] || char;
    });

  // Определяем тип атрибута на основе названия
  const lowerName = name.toLowerCase();
  let attributeType = 'simple'; // По умолчанию простой тип

  // Определяем тип на основе ключевых слов
  if (lowerName.includes('размер') || lowerName.includes('габарит') || lowerName.includes('dimension')) {
    attributeType = 'size';
  } else if (lowerName.includes('вес') || lowerName.includes('масса') || lowerName.includes('weight')) {
    attributeType = 'weight';
  } else if (lowerName.includes('цвет') || lowerName.includes('color')) {
    attributeType = 'color';
  } else if (lowerName.includes('материал') || lowerName.includes('material')) {
    attributeType = 'material';
  } else if (lowerName.includes('класс') || lowerName.includes('категория') || lowerName.includes('тип')) {
    attributeType = 'classification';
  }

  // Базовая конфигурация
  const baseConfig = {
    name: name,
    description: description || `Атрибут "${name}" для характеристики товаров`,
    icon: getDefaultIcon(attributeType),
    isSimpleArray: false,
    isGrouped: false,
    showOnProductPage: true,
    showInProductCard: false
  };

  // Настройки в зависимости от типа
  switch (attributeType) {
    case 'size':
      return {
        ...baseConfig,
        fields: [
          {
            key: 'length',
            name: 'Длина (мм)',
            type: 'number',
            required: true,
            validation: { min: 1, max: 10000 }
          },
          {
            key: 'width',
            name: 'Ширина (мм)',
            type: 'number',
            required: true,
            validation: { min: 1, max: 10000 }
          },
          {
            key: 'height',
            name: 'Высота (мм)',
            type: 'number',
            required: true,
            validation: { min: 1, max: 10000 }
          }
        ],
        display: {
          listView: ['length', 'width', 'height'],
          cardView: ['length', 'width', 'height'],
          colorField: '',
          format: '{length}×{width}×{height} мм'
        }
      };

    case 'weight':
      return {
        ...baseConfig,
        fields: [
          {
            key: 'value',
            name: 'Значение',
            type: 'number',
            required: true,
            validation: { min: 0.001, max: 10000 }
          },
          {
            key: 'unit',
            name: 'Единица измерения',
            type: 'string',
            required: true,
            validation: { pattern: '^(г|кг|т)$' }
          }
        ],
        display: {
          listView: ['value', 'unit'],
          cardView: ['value', 'unit'],
          colorField: '',
          format: '{value} {unit}'
        }
      };

    case 'material':
    case 'classification':
      return {
        ...baseConfig,
        fields: [
          {
            key: 'id',
            name: 'ID',
            type: 'string',
            required: true,
            unique: true,
            validation: {
              pattern: '^[a-z_]+$',
              message: 'ID должен содержать только строчные буквы и подчеркивания'
            }
          },
          {
            key: 'name',
            name: 'Название',
            type: 'string',
            required: true,
            validation: { minLength: 1, maxLength: 100 }
          },
          {
            key: 'description',
            name: 'Описание',
            type: 'text',
            required: false,
            validation: { maxLength: 500 }
          }
        ],
        display: {
          listView: ['name'],
          cardView: ['name', 'description'],
          colorField: '',
          format: '{name}'
        }
      };

    case 'color':
      return {
        ...baseConfig,
        fields: [
          {
            key: 'id',
            name: 'ID',
            type: 'string',
            required: true,
            unique: true,
            validation: {
              pattern: '^[a-z_]+$',
              message: 'ID должен содержать только строчные буквы и подчеркивания'
            }
          },
          {
            key: 'name',
            name: 'Название',
            type: 'string',
            required: true,
            validation: { minLength: 1, maxLength: 50 }
          },
          {
            key: 'hex',
            name: 'Hex код',
            type: 'color',
            required: true,
            validation: {
              pattern: '^#[0-9A-Fa-f]{6}$',
              message: 'Неверный формат hex-кода цвета'
            }
          }
        ],
        display: {
          listView: ['name', 'hex'],
          cardView: ['name', 'hex'],
          colorField: 'hex',
          format: '{name}'
        }
      };

    default: // simple
      return {
        ...baseConfig,
        isSimpleArray: true,
        fields: [
          {
            key: 'value',
            name: 'Значение',
            type: 'string',
            required: true,
            validation: { minLength: 1, maxLength: 100 }
          }
        ],
        display: {
          listView: ['value'],
          cardView: ['value'],
          colorField: '',
          format: '{value}'
        }
      };
  }
}

// Функция для получения иконки по умолчанию
function getDefaultIcon(attributeType) {
  const iconMap = {
    'size': 'ruler',
    'weight': 'scale',
    'material': 'layers',
    'color': 'palette',
    'classification': 'tag',
    'simple': 'info'
  };
  return iconMap[attributeType] || 'info';
}

export async function GET() {
  try {
    const data = await fs.readFile(configPath, 'utf-8');
    return new Response(data, {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка чтения конфигурации типов атрибутов' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { typeKey, config, createWithDefaults, name, description } = body;

    // Если запрос на создание с автоматическими значениями по умолчанию
    if (createWithDefaults && name) {
      const defaultConfig = createDefaultAttributeTypeConfig(name, description);
      const generatedKey = name.toLowerCase()
        .replace(/[^а-яёa-z0-9\s]/gi, '')
        .replace(/\s+/g, '_')
        .replace(/[а-яё]/g, (char) => {
          const translitMap = {
            'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',
            'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
            'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
            'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch',
            'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
          };
          return translitMap[char] || char;
        });

      // Читаем текущую конфигурацию
      let currentConfig = {};
      try {
        const data = await fs.readFile(configPath, 'utf-8');
        currentConfig = JSON.parse(data);
      } catch (error) {
        // Если файл не существует, создаем пустую конфигурацию
      }

      // Проверяем, что ключ не существует
      if (currentConfig[generatedKey]) {
        return new Response(JSON.stringify({
          error: `Тип атрибута с ключом "${generatedKey}" уже существует`
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Добавляем новый тип
      currentConfig[generatedKey] = defaultConfig;

      // Сохраняем конфигурацию
      await fs.writeFile(configPath, JSON.stringify(currentConfig, null, 2));

      // Создаем пустой массив для значений атрибута
      let attributesData = {};
      try {
        const attributesFile = await fs.readFile(attributesPath, 'utf-8');
        attributesData = JSON.parse(attributesFile);
      } catch (error) {
        // Если файл не существует, создаем пустой объект
      }

      // Добавляем пустой массив для нового типа атрибута
      if (!attributesData[generatedKey]) {
        attributesData[generatedKey] = [];
      }

      await fs.writeFile(attributesPath, JSON.stringify(attributesData, null, 2));

      return new Response(JSON.stringify({
        success: true,
        typeKey: generatedKey,
        config: defaultConfig,
        message: `Тип атрибута "${name}" успешно создан с автоматическими настройками`
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Обычное создание/обновление
    if (!typeKey || !config) {
      return new Response(JSON.stringify({ error: 'Отсутствуют обязательные данные' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Валидация конфигурации
    const validationError = validateTypeConfig(config);
    if (validationError) {
      return new Response(JSON.stringify({ error: validationError }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Загружаем текущую конфигурацию
    const configData = JSON.parse(await fs.readFile(configPath, 'utf-8'));

    // Добавляем новый тип
    configData[typeKey] = config;

    // Сохраняем конфигурацию
    await fs.writeFile(configPath, JSON.stringify(configData, null, 2), 'utf-8');

    // Создаем пустой массив для нового типа в основном файле атрибутов
    const attributesData = JSON.parse(await fs.readFile(attributesPath, 'utf-8'));
    if (!attributesData[typeKey]) {
      attributesData[typeKey] = config.isGrouped ? {} : [];
      await fs.writeFile(attributesPath, JSON.stringify(attributesData, null, 2), 'utf-8');
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Ошибка создания типа атрибута:', error);
    return new Response(JSON.stringify({ error: 'Ошибка сохранения данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function PUT({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { typeKey, config, oldTypeKey } = body;

    if (!typeKey || !config) {
      return new Response(JSON.stringify({ error: 'Отсутствуют обязательные данные' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Валидация конфигурации
    const validationError = validateTypeConfig(config);
    if (validationError) {
      return new Response(JSON.stringify({ error: validationError }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Загружаем текущую конфигурацию
    const configData = JSON.parse(await fs.readFile(configPath, 'utf-8'));

    // Если ключ изменился, переносим данные
    if (oldTypeKey && oldTypeKey !== typeKey) {
      if (configData[typeKey] && typeKey !== oldTypeKey) {
        return new Response(JSON.stringify({ error: 'Тип атрибута с таким ключом уже существует' }), {
          status: 409,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Переносим конфигурацию
      configData[typeKey] = config;
      delete configData[oldTypeKey];

      // Переносим данные в основном файле атрибутов
      const attributesData = JSON.parse(await fs.readFile(attributesPath, 'utf-8'));
      if (attributesData[oldTypeKey]) {
        attributesData[typeKey] = attributesData[oldTypeKey];
        delete attributesData[oldTypeKey];
        await fs.writeFile(attributesPath, JSON.stringify(attributesData, null, 2), 'utf-8');
      }
    } else {
      // Просто обновляем конфигурацию
      configData[typeKey] = config;
    }

    // Сохраняем конфигурацию
    await fs.writeFile(configPath, JSON.stringify(configData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Ошибка обновления типа атрибута:', error);
    return new Response(JSON.stringify({ error: 'Ошибка сохранения данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function DELETE({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { typeKey } = body;

    if (!typeKey) {
      return new Response(JSON.stringify({ error: 'Отсутствует ключ типа атрибута' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Загружаем текущую конфигурацию
    const configData = JSON.parse(await fs.readFile(configPath, 'utf-8'));

    if (!configData[typeKey]) {
      return new Response(JSON.stringify({ error: 'Тип атрибута не найден' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Удаляем из конфигурации
    delete configData[typeKey];

    // Удаляем из основного файла атрибутов
    const attributesData = JSON.parse(await fs.readFile(attributesPath, 'utf-8'));
    if (attributesData[typeKey]) {
      delete attributesData[typeKey];
      await fs.writeFile(attributesPath, JSON.stringify(attributesData, null, 2), 'utf-8');
    }

    // Сохраняем конфигурацию
    await fs.writeFile(configPath, JSON.stringify(configData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Ошибка удаления типа атрибута:', error);
    return new Response(JSON.stringify({ error: 'Ошибка удаления данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Функция валидации конфигурации типа атрибута
function validateTypeConfig(config) {
  if (!config.name || typeof config.name !== 'string') {
    return 'Название типа атрибута обязательно';
  }

  if (!config.fields || !Array.isArray(config.fields) || config.fields.length === 0) {
    return 'Должно быть определено хотя бы одно поле';
  }

  // Проверяем поля отображения
  if (config.hasOwnProperty('showOnProductPage') && typeof config.showOnProductPage !== 'boolean') {
    return 'Поле showOnProductPage должно быть булевым значением';
  }

  if (config.hasOwnProperty('showInProductCard') && typeof config.showInProductCard !== 'boolean') {
    return 'Поле showInProductCard должно быть булевым значением';
  }

  // Проверяем каждое поле
  for (const field of config.fields) {
    if (!field.key || typeof field.key !== 'string') {
      return 'Каждое поле должно иметь ключ';
    }

    if (!field.name || typeof field.name !== 'string') {
      return 'Каждое поле должно иметь название';
    }

    if (!field.type || typeof field.type !== 'string') {
      return 'Каждое поле должно иметь тип';
    }

    const allowedTypes = ['string', 'number', 'text', 'color', 'boolean', 'select'];
    if (!allowedTypes.includes(field.type)) {
      return `Неподдерживаемый тип поля: ${field.type}`;
    }
  }

  return null; // Валидация прошла успешно
}
