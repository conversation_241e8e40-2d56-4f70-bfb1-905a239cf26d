# Документация страницы Продукция

## Назначение

Страница `src/pages/products.astro` реализует каталог товаров компании, предоставляя пользователям возможность просматривать, фильтровать и сортировать продукцию.

## Структура UI

Страница имеет адаптивный двухколоночный макет на больших экранах и одноколоночный макет на мобильных:

-   **Сайдбар с фильтрами (слева на десктопе, сверху на мобильных):** Содержит блоки для фильтрации товаров. На мобильных устройствах сайдбар скрыт по умолчанию и может быть открыт по кнопке "Фильтры".
    -   Фильтр по категориям (чекбоксы, адаптировано из предыдущей версии).
    -   Placeholder для фильтра по цене (Диапазон цены Placeholder).
    -   Placeholder для других фильтров (Назначение / Параметры Placeholder).

-   **Основная область каталога (справа на десктопе, снизу на мобильных):** Содержит инструменты сортировки, переключения вида и список карточек товаров.
    -   **Панель управления:** Включает выпадающий список для сортировки товаров и кнопки для переключения между сеточным (Grid) и списочным (List) видом карточек.
    -   **Список товаров:** Отображает карточки товаров в выбранном виде (по умолчанию - сетка 3 в ряд на широких экранах).

## Данные товаров

Данные о товарах загружаются из файла `aizen-astro/data/product/products.json`. Этот файл представляет собой массив объектов, каждый из которых описывает отдельный товар. Подробную схему данных можно найти в `aizen-astro/data/product/schema.md`. Пути к изображениям, указанные в этом файле (например, `media/TB-001/main.jpg`), являются относительными к директории `public/product/media/`.

Основные поля, используемые на странице каталога:
-   `id`: Уникальный артикул товара (используется для ссылок на детальную страницу).
-   `name`: Название товара.
-   `category`: Основная категория товара (используется для фильтрации).
-   `shortDescription`: Краткое описание товара (отображается в карточке).
-   `basePrice.value`: Стоимость товара.
-   `basePrice.unit`: Единица измерения цены.
-   `images.main`: Путь к основному изображению товара (относительно `public/product/media/`).

## Реализация UI и Логики

-   **Макет:** Используются утилитарные классы Tailwind CSS Grid для создания адаптивной структуры.
-   **Фильтрация:** Базовая фильтрация по категориям реализована на стороне клиента с использованием JavaScript. При выборе чекбоксов товаров скрываются или отображаются соответствующие карточки.
-   **Сортировка:** Реализован только placeholder для выбора опции сортировки. Логика самой сортировки данных и перерисовки списка требует дальнейшей реализации.
-   **Переключение вида:** Реализовано переключение классов Tailwind на контейнере списка товаров для изменения его отображения между сеткой (`display: grid`, `grid-template-columns`) и списком (`display: flex`, `flex-direction: column`).
-   **Клиентский JavaScript:** Встроенный `<script>` блок (`is:inline`) содержит логику для управления сайдбаром на мобильных устройствах, базовой фильтрации и переключения вида. Обратите внимание, что для корректной работы на стороне клиента используется директива `client:only`. 

## Кастомизация карточек товаров

Стили карточек товаров определены в файле `src/pages/products.astro` с использованием классов Tailwind. Карточки вертикального вида, без закруглений, соответствуют общей стилистике шаблона. 

-   Изображение товара: `product.images.main` (путь относительно `public/product/media/`)
-   Категория: `product.category`
-   Название: `product.name`
-   Цена: `product.basePrice.value` и `product.basePrice.unit`
-   Описание: `product.shortDescription`
-   Ссылка "Подробнее": `/products/${product.id}`

## Дальнейшие шаги

-   Реализация полной логики фильтрации (по цене, атрибутам).
-   Реализация логики сортировки товаров.
-   Добавление пагинации или бесконечной прокрутки для большого количества товаров.
-   Подключение реальных данных и изображений товаров.
-   Создание детальной страницы товара (`src/pages/products/[id].astro`). 