/**
 * Пример интеграции данных о товарах с фронтенд-частью на AstroJS
 * 
 * Этот файл содержит примеры кода для загрузки и отображения данных о товарах
 * в различных компонентах AstroJS.
 */

// Пример загрузки всех товаров в компоненте Astro
// Файл: /apps/frontend/src/pages/catalog/index.astro

```astro
---
// Импорт данных о товарах
import productsData from '../../../data/product/products.json';

// Фильтрация товаров по категории (опционально)
const category = Astro.url.searchParams.get('category');
const products = category 
  ? productsData.filter(product => product.category === category)
  : productsData;
---

<Layout title="Каталог товаров | LuxBeton">
  <div class="container mx-auto py-8">
    <h1 class="text-3xl font-bold mb-6">Каталог товаров</h1>
    
    <!-- Фильтр по категориям -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Категории</h2>
      <div class="flex flex-wrap gap-2">
        <a href="/catalog" class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300">
          Все товары
        </a>
        {Array.from(new Set(productsData.map(p => p.category))).map(cat => (
          <a 
            href={`/catalog?category=${encodeURIComponent(cat)}`}
            class={`px-4 py-2 rounded hover:bg-gray-300 ${
              category === cat ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            {cat}
          </a>
        ))}
      </div>
    </div>
    
    <!-- Список товаров -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {products.map(product => (
        <div class="border rounded overflow-hidden shadow-sm hover:shadow-md transition">
          <img 
            src={`/data/product/media/${product.images.main}`} 
            alt={product.name}
            class="w-full h-64 object-cover"
          />
          <div class="p-4">
            <h3 class="text-xl font-semibold mb-2">{product.name}</h3>
            <p class="text-gray-600 mb-4">{product.shortDescription}</p>
            <div class="flex justify-between items-center">
              <span class="text-lg font-bold">
                {product.basePrice.value} ₽/{product.basePrice.unit}
              </span>
              <a 
                href={`/catalog/${product.id}`}
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
              >
                Подробнее
              </a>
            </div>
          </div>
        </div>
      ))}
    </div>
    
    {products.length === 0 && (
      <div class="text-center py-12">
        <p class="text-xl text-gray-600">Товары не найдены</p>
      </div>
    )}
  </div>
</Layout>
```

// Пример страницы отдельного товара
// Файл: /apps/frontend/src/pages/catalog/[id].astro

```astro
---
import Layout from '../../layouts/Layout.astro';
import { getProductById, getColorHex } from '../../utils/products';

// Получение ID товара из параметров URL
const { id } = Astro.params;

// Поиск товара по ID
const product = await getProductById(id);

// Если товар не найден, перенаправляем на страницу каталога
if (!product) {
  return Astro.redirect('/catalog');
}

// Загрузка данных о категориях для отображения хлебных крошек
import categoriesData from '../../../data/product/categories.json';
const categoryInfo = categoriesData.categories.find(c => c.name === product.category);
---

<Layout title={`${product.name} | LuxBeton`}>
  <div class="container mx-auto py-8 px-4">
    <!-- Хлебные крошки -->
    <div class="flex items-center text-sm text-gray-600 mb-6">
      <a href="/" class="hover:text-blue-500">Главная</a>
      <span class="mx-2">/</span>
      <a href="/catalog" class="hover:text-blue-500">Каталог</a>
      <span class="mx-2">/</span>
      <a href={`/catalog?category=${encodeURIComponent(product.category)}`} class="hover:text-blue-500">
        {product.category}
      </a>
      <span class="mx-2">/</span>
      <span class="text-gray-900">{product.name}</span>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Галерея изображений -->
      <div>
        <div class="mb-4">
          <img 
            src={`/data/product/media/${product.images.main}`} 
            alt={product.name}
            class="w-full h-auto rounded"
            id="main-image"
          />
        </div>
        <div class="grid grid-cols-6 gap-2">
          <div 
            class="cursor-pointer border-2 border-blue-500 rounded overflow-hidden"
          >
            <img 
              src={`/data/product/media/${product.images.main}`} 
              alt="Основное изображение"
              class="w-full h-auto"
              onclick="document.getElementById('main-image').src=this.src"
            />
          </div>
          {product.images.additional.map((img, index) => (
            <div 
              class="cursor-pointer border-2 border-transparent hover:border-blue-500 rounded overflow-hidden"
            >
              <img 
                src={`/data/product/media/${img}`} 
                alt={`Изображение ${index + 1}`}
                class="w-full h-auto"
                onclick="document.getElementById('main-image').src=this.src"
              />
            </div>
          ))}
        </div>
      </div>
      
      <!-- Информация о товаре -->
      <div>
        <h1 class="text-3xl font-bold mb-2">{product.name}</h1>
        <p class="text-gray-600 mb-4">{product.shortDescription}</p>
        
        <div class="mb-6">
          <div class="text-2xl font-bold text-blue-600">
            {product.basePrice.value} ₽/{product.basePrice.unit}
          </div>
          <div class="mt-2 flex items-center">
            <span class={`inline-block w-3 h-3 rounded-full mr-1 ${product.inStock ? 'bg-green-500' : 'bg-red-500'}`}></span>
            <span>{product.inStock ? 'В наличии' : 'Под заказ'}</span>
          </div>
        </div>
        
        <!-- Атрибуты товара -->
        <div class="mb-6">
          <h2 class="text-xl font-semibold mb-3">Характеристики</h2>
          <div class="grid grid-cols-2 gap-y-2">
            <div class="text-gray-600">Артикул:</div>
            <div>{product.id}</div>
            
            <div class="text-gray-600">Категория:</div>
            <div>{product.category} / {product.subcategory}</div>
            
            <div class="text-gray-600">Размеры:</div>
            <div>
              {product.attributes.size.length && product.attributes.size.width && product.attributes.size.height ? 
                `${product.attributes.size.length}×${product.attributes.size.width}×${product.attributes.size.height} мм` :
                'Различные размеры'}
            </div>
            
            <div class="text-gray-600">Вес:</div>
            <div>{product.attributes.weight} кг</div>
            
            <div class="text-gray-600">Прочность:</div>
            <div>{product.attributes.strength}</div>
            
            <div class="text-gray-600">Фактура:</div>
            <div>{product.attributes.texture}</div>
          </div>
        </div>
        
        <!-- Доступные цвета -->
        <div class="mb-6">
          <h2 class="text-xl font-semibold mb-3">Доступные цвета</h2>
          <div class="flex flex-wrap gap-2">
            {product.attributes.colors.map(color => (
              <div class="flex items-center">
                <span 
                  class="inline-block w-6 h-6 rounded-full mr-2 border" 
                  style={`background-color: ${getColorHex(color)}`}
                ></span>
                <span>{color}</span>
              </div>
            ))}
          </div>
        </div>
        
        <!-- Кнопка заказа -->
        <div class="mt-8">
          <button 
            class="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
            data-product-id={product.id}
            data-product-name={product.name}
            id="order-button"
          >
            Оформить заказ
          </button>
        </div>
      </div>
    </div>
    
    <!-- Полное описание товара -->
    <div class="mt-12">
      <h2 class="text-2xl font-semibold mb-4">Описание</h2>
      <div class="prose max-w-none">
        <p>{product.fullDescription}</p>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Функция для получения HEX-кода цвета из справочника
  function getColorHex(colorName) {
    // Здесь можно реализовать логику получения HEX-кода из attributes.json
    // Для простоты используем базовые цвета
    const colorMap = {
      'серый': '#808080',
      'красный': '#B22222',
      'коричневый': '#8B4513',
      'желтый': '#FFD700',
      'черный': '#000000',
      'белый': '#FFFFFF',
      'песочный': '#F5DEB3',
      'терракотовый': '#E2725B'
    };
    return colorMap[colorName.toLowerCase()] || '#CCCCCC';
  }
  
  // Обработчик кнопки заказа
  document.getElementById('order-button')?.addEventListener('click', function() {
    const productId = this.getAttribute('data-product-id');
    const productName = this.getAttribute('data-product-name');
    // Здесь можно реализовать логику открытия модального окна с формой заказа
    console.log(`Заказ товара: ${productId} - ${productName}`);
    // Пример: window.location.href = `/order-form?product=${productId}`;
  });
</script>
```

// Пример компонента для отображения популярных товаров на главной странице
// Файл: /apps/frontend/src/components/PopularProducts.astro

```astro
---
import { getPopularProducts } from '../utils/products';

// Получение популярных товаров (сортировка по рейтингу популярности)
const popularProducts = await getPopularProducts(4); // Берем только 4 самых популярных товара
---

<section class="py-12 bg-gray-50">
  <div class="container mx-auto">
    <h2 class="text-3xl font-bold text-center mb-8">Популярные товары</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {popularProducts.map(product => (
        <div class="bg-white border rounded overflow-hidden shadow-sm hover:shadow-md transition">
          <img 
            src={`/images/products/${product.images.main}`} 
            alt={product.name}
            class="w-full h-48 object-cover"
          />
          <div class="p-4">
            <h3 class="text-lg font-semibold mb-2">{product.name}</h3>
            <p class="text-gray-600 text-sm mb-3 line-clamp-2">{product.shortDescription}</p>
            <div class="flex justify-between items-center">
              <span class="font-bold">{product.basePrice.value} ₽</span>
              <a 
                href={`/catalog/${product.id}`}
                class="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition"
              >
                Подробнее
              </a>
            </div>
          </div>
        </div>
      ))}
    </div>
    
    <div class="text-center mt-8">
      <a 
        href="/catalog"
        class="inline-block px-6 py-3 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
      >
        Перейти в каталог
      </a>
    </div>
  </div>
</section>
```

// Пример API-эндпоинта для получения данных о товарах
// Файл: /apps/frontend/src/pages/api/products.json.js

```js
import productsData from '@data/product/products.json';

export async function GET({ request }) {
  // Получение параметров запроса
  const url = new URL(request.url);
  const category = url.searchParams.get('category');
  const subcategory = url.searchParams.get('subcategory');
  const limit = parseInt(url.searchParams.get('limit') || '0');
  
  // Фильтрация товаров
  let filteredProducts = [...productsData];
  
  if (category) {
    filteredProducts = filteredProducts.filter(p => p.category === category);
  }
  
  if (subcategory) {
    filteredProducts = filteredProducts.filter(p => p.subcategory === subcategory);
  }
  
  // Ограничение количества товаров
  if (limit > 0) {
    filteredProducts = filteredProducts.slice(0, limit);
  }
  
  return new Response(JSON.stringify(filteredProducts), {
    status: 200,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}
```

// Пример утилиты для работы с данными о товарах
// Файл: /apps/frontend/src/utils/products.js

```js
import productsData from '@data/product/products.json';

/**
 * Получение товара по ID
 * @param {string} id - ID товара
 * @returns {Object|null} - Объект товара или null, если товар не найден
 */
export async function getProductById(id) {
  return productsData.find(p => p.id === id) || null;
}

/**
 * Получение популярных товаров
 * @param {number} limit - Количество товаров для возврата
 * @returns {Array} - Массив популярных товаров
 */
export async function getPopularProducts(limit = 4) {
  return [...productsData]
    .sort((a, b) => b.popularity - a.popularity)
    .slice(0, limit);
}

/**
 * Получение товаров по категории
 * @param {string} category - Название категории
 * @param {string} subcategory - Название подкатегории (опционально)
 * @returns {Array} - Массив товаров
 */
export async function getProductsByCategory(category, subcategory = null) {
  let filtered = productsData.filter(p => p.category === category);
  
  if (subcategory) {
    filtered = filtered.filter(p => p.subcategory === subcategory);
  }
  
  return filtered;
}
```