---
import AdminLayout from '../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../utils/auth';
import Card from '../../../components/ui/Card.astro';
import CardHeader from '../../../components/ui/CardHeader.astro';
import CardTitle from '../../../components/ui/CardTitle.astro';
import CardDescription from '../../../components/ui/CardDescription.astro';
import CardContent from '../../../components/ui/CardContent.astro';
import Button from '../../../components/ui/Button.astro';
import Badge from '../../../components/ui/Badge.astro';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Загрузка данных об атрибутах
import attributesFile from '../../../../data/product/attributes.json';
const attributesData = attributesFile;

// Загрузка конфигурации типов атрибутов
import fs from 'node:fs/promises';
import path from 'node:path';

let attributeTypesConfig = {};
try {
  const configPath = path.join(process.cwd(), 'data', 'product', 'attribute-types-config.json');
  const configData = await fs.readFile(configPath, 'utf-8');
  attributeTypesConfig = JSON.parse(configData);
} catch (error) {
  console.error('Ошибка загрузки конфигурации типов атрибутов:', error);
}

// Функция для получения всех типов атрибутов из конфигурации и данных
function getAllAttributeTypes(data, config) {
  const allTypes = {};

  console.log('Server getAllAttributeTypes - data keys:', Object.keys(data));
  console.log('Server getAllAttributeTypes - config keys:', Object.keys(config));

  // Добавляем типы из конфигурации
  Object.keys(config).forEach(key => {
    allTypes[key] = config[key].name || key;
  });

  // Добавляем типы из данных, которых нет в конфигурации (для обратной совместимости)
  Object.keys(data).forEach(key => {
    if (!allTypes[key]) {
      // Преобразуем ключ в читаемое название
      allTypes[key] = key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ');
    }
  });

  console.log('Server getAllAttributeTypes - result:', allTypes);
  return allTypes;
}

// Получаем все типы атрибутов
const attributeTypes = getAllAttributeTypes(attributesData, attributeTypesConfig);

// Проверяем, что у нас есть типы атрибутов для отображения
console.log('Server-side attributeTypes:', attributeTypes);

// Если нет типов атрибутов, создаем базовый набор
if (Object.keys(attributeTypes).length === 0) {
  console.log('No attribute types found, using fallback');
  // Добавляем базовые типы как fallback
  attributeTypes.colors = 'Цвет';
  attributeTypes.textures = 'Текстура';
  attributeTypes.strength_classes = 'Класс прочности';
}

// Получаем параметр tab из URL для установки активной вкладки
const url = new URL(Astro.request.url);
const activeTab = url.searchParams.get('tab') || 'colors';

// Функция для подсчета количества элементов в каждом типе атрибута
function getAttributeCount(attributeType, data) {
  if (!data[attributeType]) return 0;

  if (attributeType === 'sizes') {
    // Для размеров считаем общее количество элементов во всех типах
    return Object.values(data[attributeType]).reduce((total, sizes) => total + sizes.length, 0);
  } else if (Array.isArray(data[attributeType])) {
    return data[attributeType].length;
  } else if (typeof data[attributeType] === 'object') {
    return Object.keys(data[attributeType]).length;
  }

  return 0;
}
---

<AdminLayout title="Управление атрибутами | LuxBeton">
  <div class="container mx-auto py-8 px-4 sm:px-6 lg:px-8" style="overflow: visible;">
    <!-- Header Section -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Управление атрибутами</h1>
          <p class="mt-2 text-sm text-gray-600">Управляйте типами атрибутов и их значениями для товаров</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3">
          <a
            href="/admin/attributes/quick-create"
            class="create-type-btn inline-flex items-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
            style="background-color: #3b82f6;"
            title="Быстрое создание с автоматическими настройками"
          >
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
            </svg>
            Быстрое создание
          </a>

          <a
            href="/admin/attributes/new-type"
            class="create-type-btn inline-flex items-center rounded-md px-3 py-2 text-sm font-semibold border border-blue-600 text-blue-600 bg-white hover:bg-blue-50 shadow-sm transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
            title="Создание с ручными настройками"
          >
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
            </svg>
            Ручное создание
          </a>

          <button
            id="settings-btn"
            class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            title="Настройки отображения"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Настройки
          </button>
        </div>
      </div>
    </div>

    <!-- Tabs Navigation -->
    <div class="mb-6" style="overflow: visible;">
      <div class="border-b border-gray-200" style="overflow: visible;">
        <nav id="tabs-container" class="-mb-px flex items-end justify-between" style="overflow: visible;">
          <!-- Вкладки будут созданы динамически через JavaScript -->
          <div class="flex items-center justify-center py-8">
            <div class="text-gray-500">Загрузка...</div>
          </div>
        </nav>
      </div>
    </div>

    <!-- Tab Content -->
    {Object.entries(attributeTypes).map(([key, name], index) => (
      <div
        id={`tab-${key}`}
        class={`tab-content ${key === activeTab ? '' : 'hidden'}`}
      >
        <Card>
          <CardHeader>
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <CardTitle class="text-xl">{name}</CardTitle>
              </div>
              <div class="flex flex-col sm:flex-row gap-2">
                <a
                  href={`/admin/attributes/new?type=${encodeURIComponent(key)}`}
                  class="inline-flex items-center px-3 py-1.5 border border-blue-300 text-xs font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors w-full sm:w-auto justify-center"
                  title="Добавить значение атрибута"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                  </svg>
                  Добавить значение
                </a>
                <a
                  href={`/admin/attributes/edit-type/${key}`}
                  class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 transition-colors w-full sm:w-auto justify-center"
                  title="Редактировать тип атрибута"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                  Редактировать тип
                </a>
                <button
                  class="delete-attribute-type inline-flex items-center px-3 py-1.5 border border-red-300 text-xs font-medium rounded text-red-700 bg-red-50 hover:bg-red-100 transition-colors w-full sm:w-auto justify-center"
                  data-type={key}
                  title="Удалить тип атрибута"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                  Удалить тип
                </button>
              </div>
            </div>
          </CardHeader>
          <CardContent class="p-0">
            <!-- Desktop Table -->
            <div class="hidden lg:block overflow-x-auto custom-scrollbar">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    {key === 'colors' && (
                      <>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Название</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Цвет</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hex</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                      </>
                    )}
                    {key === 'textures' && (
                      <>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Название</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                      </>
                    )}
                    {(key === 'strength_classes' || key === 'frost_resistance' || key === 'water_absorption') && (
                      <>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Класс</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Описание</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                      </>
                    )}
                    {key === 'standard_sizes' && (
                      <>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Тип</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Размеры</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                      </>
                    )}
                    {(key === 'surfaces' || key === 'patterns' || key === 'color_pigments') && (
                      <>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Название</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Описание</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                      </>
                    )}
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id={`tbody-${key}`}>
                  <!-- Содержимое будет загружено через JavaScript -->
                </tbody>
              </table>
            </div>

            <!-- Mobile Cards -->
            <div class="lg:hidden space-y-4 p-6" id={`mobile-${key}`}>
              <!-- Содержимое будет загружено через JavaScript -->
            </div>
          </CardContent>
        </Card>
      </div>
    ))}
  </div>

  <!-- Модальное окно настроек -->
  <div id="settings-modal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-[99999]">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="bg-white rounded-lg shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-gray-200">
        <!-- Header -->
        <div class="px-6 py-5 border-b border-gray-200 bg-gray-50">
          <div class="flex items-center justify-between">
            <h3 class="text-xl font-semibold text-gray-900">Настройки отображения атрибутов</h3>
            <button
              id="close-settings-btn"
              class="text-gray-400 hover:text-gray-600 focus:outline-none transition-colors p-1 rounded-md hover:bg-gray-200"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Content -->
        <div class="px-6 py-6">
          <div class="space-y-8">
            <!-- Порядок вкладок -->
            <div>
              <h4 class="text-lg font-semibold text-gray-900 mb-3">Порядок отображения вкладок</h4>
              <p class="text-sm text-gray-600 mb-5 leading-relaxed">
                Перетащите типы атрибутов для изменения порядка их отображения в интерфейсе.
                Первые 6 типов будут отображаться как основные вкладки, остальные - в выпадающем меню.
              </p>

              <div id="sortable-tabs" class="space-y-2">
                <!-- Элементы будут добавлены через JavaScript -->
              </div>
            </div>

            <!-- Настройки отображения -->
            <div class="border-t border-gray-200 pt-8">
              <h4 class="text-lg font-semibold text-gray-900 mb-5">Настройки отображения</h4>

              <div class="space-y-6">
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <div>
                    <label class="text-sm font-semibold text-gray-800">Количество основных вкладок</label>
                    <p class="text-xs text-gray-600 mt-1">Сколько вкладок показывать до выпадающего меню</p>
                  </div>
                  <select
                    id="visible-tabs-count"
                    class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                  >
                    <option value="4">4 вкладки</option>
                    <option value="5">5 вкладок</option>
                    <option value="6" selected>6 вкладок</option>
                    <option value="7">7 вкладок</option>
                    <option value="8">8 вкладок</option>
                  </select>
                </div>

                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <div>
                    <label class="text-sm font-semibold text-gray-800">Показывать счетчики</label>
                    <p class="text-xs text-gray-600 mt-1">Отображать количество элементов в каждом типе</p>
                  </div>
                  <input
                    type="checkbox"
                    id="show-counters"
                    checked
                    class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>

                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <div>
                    <label class="text-sm font-semibold text-gray-800">Компактный режим</label>
                    <p class="text-xs text-gray-600 mt-1">Уменьшенные отступы и размеры элементов</p>
                  </div>
                  <input
                    type="checkbox"
                    id="compact-mode"
                    class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="px-6 py-5 border-t border-gray-200 bg-gray-50 flex justify-end space-x-3">
          <button
            id="reset-settings-btn"
            class="px-5 py-2.5 border border-gray-300 text-sm font-semibold rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            Сбросить
          </button>
          <button
            id="save-settings-btn"
            class="save-settings-btn px-5 py-2.5 border border-transparent text-sm font-semibold rounded-md text-white transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            style="background-color: #3b82f6;"
          >
            Сохранить
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Уведомления -->
  <div id="notification" class="fixed top-4 right-4 z-50 hidden">
    <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm">
      <div class="flex items-center">
        <div id="notification-icon" class="flex-shrink-0 mr-3"></div>
        <div id="notification-message" class="text-sm text-gray-700"></div>
      </div>
    </div>
  </div>
</AdminLayout>

<style>
  /* Анимации */
  @keyframes slide-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-slide-in {
    animation: slide-in 0.5s ease-out forwards;
  }

  /* Скрытие скроллбара для горизонтального скролла */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Кастомный скроллбар для таблиц */
  .custom-scrollbar::-webkit-scrollbar {
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Адаптивные стили для мобильных карточек */
  @media (max-width: 1023px) {
    .mobile-card {
      transition: all 0.2s ease-in-out;
    }

    .mobile-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  /* Стили для выпадающего меню */
  #more-tabs-dropdown {
    z-index: 9999 !important;
    position: absolute !important;
  }

  /* Обеспечиваем правильное позиционирование родительского элемента */
  .relative {
    position: relative;
  }

  /* Убеждаемся, что таблица не создает новый контекст стекинга */
  .tab-content {
    position: relative;
    z-index: 1;
  }

  /* Навигация должна быть выше таблицы */
  #tabs-container {
    position: relative;
    z-index: 100;
  }

  /* Контейнер для выпадающего меню должен иметь высокий z-index */
  .dropdown-container {
    position: relative;
    z-index: 1000;
  }

  /* Убеждаемся, что карточки не перекрывают выпадающее меню */
  .tab-content .bg-white {
    position: relative;
    z-index: auto;
  }

  /* Дополнительные стили для корректного отображения выпадающего меню */
  .dropdown-container {
    isolation: isolate;
  }

  /* Убеждаемся, что выпадающее меню отображается поверх всех элементов */
  #more-tabs-dropdown {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    background: white;
  }

  /* Убеждаемся, что контейнер страницы не создает новый контекст стекинга */
  .container {
    position: relative;
    z-index: auto;
    overflow: visible !important;
  }

  /* Обеспечиваем видимость выпадающего меню */
  .tab-content {
    overflow: visible !important;
  }

  /* Убеждаемся, что все родительские элементы позволяют отображение выпадающего меню */
  nav, .border-b {
    overflow: visible !important;
  }

  /* Стили для компактного режима */
  .compact-mode .tab-button {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }

  .compact-mode .mobile-card {
    padding: 0.75rem;
  }

  .compact-mode .mobile-card h3 {
    font-size: 0.75rem;
  }

  .compact-mode .mobile-card p {
    font-size: 0.625rem;
  }

  .compact-mode .container {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  /* Стили для drag & drop */
  .opacity-50 {
    opacity: 0.5;
  }

  /* Стили для модального окна */
  #settings-modal {
    backdrop-filter: blur(4px);
    z-index: 99999 !important;
    position: fixed !important;
  }

  #settings-modal .bg-white {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    z-index: 100000;
  }

  /* Улучшенные стили для drag & drop элементов */
  #sortable-tabs .cursor-move:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  #sortable-tabs .cursor-move:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  /* Анимации для уведомлений */
  #notification {
    transition: all 0.3s ease-in-out;
  }

  #notification.hidden {
    opacity: 0;
    transform: translateX(100%);
  }

  #notification:not(.hidden) {
    opacity: 1;
    transform: translateX(0);
  }

  /* Стили для цветовых индикаторов */
  .color-indicator {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
  }

  /* Стили для мобильных карточек */
  .mobile-card {
    transition: all 0.2s ease-in-out;
  }

  .mobile-card:hover {
    transform: translateY(-1px);
  }

  /* Скрытие полосы прокрутки для горизонтальных вкладок */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Стили для выпадающего меню */
  .dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
  }

  /* Стили для кнопки создания типа атрибута */
  .create-type-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки сохранения настроек */
  .save-settings-btn:hover {
    background-color: #2563eb !important;
  }
</style>

<script define:vars={{ activeTab }}>
  // Глобальные переменные
  let currentAttributeType = activeTab || 'colors';
  let attributesData = {};
  let allAttributeTypes = {};
  let attributeTypesConfig = {};
  let displaySettings = {
    tabOrder: [],
    visibleTabsCount: 6,
    showCounters: true,
    compactMode: false
  };

  // Функция для получения всех типов атрибутов из конфигурации и данных
  function getAllAttributeTypes(data, config) {
    const allTypes = {};

    console.log('getAllAttributeTypes called with:', { data: Object.keys(data), config: Object.keys(config) });

    // Добавляем типы из конфигурации
    Object.keys(config).forEach(key => {
      allTypes[key] = config[key].name || key;
    });

    // Добавляем типы из данных, которых нет в конфигурации (для обратной совместимости)
    Object.keys(data).forEach(key => {
      if (!allTypes[key]) {
        // Преобразуем ключ в читаемое название
        allTypes[key] = key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ');
      }
    });

    console.log('getAllAttributeTypes result:', allTypes);
    return allTypes;
  }

  // Функция для получения упорядоченного списка типов атрибутов
  function getOrderedAttributeTypes() {
    const allTypesEntries = Object.entries(allAttributeTypes);

    // Если настройки порядка не загружены или пусты, возвращаем исходный порядок
    if (!displaySettings || !displaySettings.tabOrder || displaySettings.tabOrder.length === 0) {
      console.log('Используем исходный порядок типов атрибутов');
      return allTypesEntries;
    }

    // Создаем упорядоченный список на основе настроек
    const orderedTypes = [];
    const remainingTypes = new Map(allTypesEntries);

    // Сначала добавляем типы в порядке из настроек
    displaySettings.tabOrder.forEach(key => {
      if (remainingTypes.has(key)) {
        orderedTypes.push([key, remainingTypes.get(key)]);
        remainingTypes.delete(key);
      }
    });

    // Затем добавляем оставшиеся типы
    remainingTypes.forEach((name, key) => {
      orderedTypes.push([key, name]);
    });

    console.log('Упорядоченные типы атрибутов:', orderedTypes.map(([key]) => key));
    return orderedTypes;
  }

  // Загрузка данных атрибутов
  async function loadAttributes() {
    console.log('loadAttributes called'); // Отладка
    try {
      // Загружаем данные атрибутов
      const attributesResponse = await fetch('/api/admin/attributes');
      attributesData = await attributesResponse.json();

      // Загружаем конфигурацию типов атрибутов
      const configResponse = await fetch('/api/admin/attribute-types-config');
      attributeTypesConfig = await configResponse.json();

      allAttributeTypes = getAllAttributeTypes(attributesData, attributeTypesConfig);
      console.log('Loaded attributes:', { attributesData, attributeTypesConfig, allAttributeTypes }); // Отладка

      // НЕ вызываем updateTabsDisplay здесь - это будет сделано после загрузки настроек
    } catch (error) {
      console.error('Ошибка загрузки атрибутов:', error);
    }
  }

  // Загрузка настроек отображения
  async function loadDisplaySettings() {
    try {
      const response = await fetch('/api/admin/attributes-display-settings');
      if (response.ok) {
        displaySettings = await response.json();
        console.log('Настройки отображения загружены:', displaySettings);
      } else {
        console.warn('Не удалось загрузить настройки отображения, используем значения по умолчанию');
        // Устанавливаем настройки по умолчанию
        displaySettings = {
          tabOrder: [],
          visibleTabsCount: 6,
          showCounters: true,
          compactMode: false
        };
      }

      // Применяем настройки к интерфейсу
      applyDisplaySettings();
    } catch (error) {
      console.error('Ошибка загрузки настроек отображения:', error);
      // Устанавливаем настройки по умолчанию при ошибке
      displaySettings = {
        tabOrder: [],
        visibleTabsCount: 6,
        showCounters: true,
        compactMode: false
      };
      applyDisplaySettings();
    }
  }

  // Применение настроек отображения к интерфейсу
  function applyDisplaySettings() {
    // Применяем компактный режим
    if (displaySettings.compactMode) {
      document.body.classList.add('compact-mode');
    } else {
      document.body.classList.remove('compact-mode');
    }

    // Обновляем отображение вкладок с учетом настроек
    if (Object.keys(allAttributeTypes).length > 0) {
      updateTabsDisplay();
      renderCurrentTab(); // Рендерим текущую вкладку после обновления
    }
  }

  // Функция для получения иконок статуса отображения
  function getDisplayStatusIcons(typeKey) {
    const config = attributeTypesConfig[typeKey];
    if (!config) return '';

    const showOnProductPage = config.showOnProductPage !== false;
    const showInProductCard = config.showInProductCard === true;

    const pageIcon = showOnProductPage
      ? '<span class="ml-1 text-green-500" title="Показывается на странице товара">●</span>'
      : '<span class="ml-1 text-red-500" title="Не показывается на странице товара">●</span>';

    const cardIcon = showInProductCard
      ? '<span class="ml-1 text-green-500" title="Показывается в карточке товара">▲</span>'
      : '<span class="ml-1 text-red-500" title="Не показывается в карточке товара">▲</span>';

    return `${pageIcon}${cardIcon}`;
  }

  // Функция для обновления отображения вкладок
  function updateTabsDisplay() {
    console.log('updateTabsDisplay called'); // Отладка
    console.log('allAttributeTypes:', allAttributeTypes); // Отладка
    console.log('displaySettings:', displaySettings); // Отладка

    const tabsContainer = document.getElementById('tabs-container');
    if (!tabsContainer) {
      console.log('tabs-container not found'); // Отладка
      return;
    }

    // Проверяем, что у нас есть данные для отображения
    if (!allAttributeTypes || Object.keys(allAttributeTypes).length === 0) {
      console.log('No attribute types to display'); // Отладка
      return;
    }

    // Очищаем контейнер
    tabsContainer.innerHTML = '';

    // Получаем упорядоченный список типов атрибутов
    const orderedTypes = getOrderedAttributeTypes();
    const visibleTabsCount = (displaySettings && displaySettings.visibleTabsCount) || 6;
    const visibleTabs = orderedTypes.slice(0, visibleTabsCount);
    const hiddenTabs = orderedTypes.slice(visibleTabsCount);

    console.log('Tabs info:', { total: orderedTypes.length, visible: visibleTabs.length, hidden: hiddenTabs.length }); // Отладка

    // Создаем контейнер для основных вкладок
    const mainTabsContainer = document.createElement('div');
    mainTabsContainer.className = 'flex space-x-1 sm:space-x-4 lg:space-x-8 items-end overflow-x-auto scrollbar-hide';

    // Создаем видимые вкладки
    visibleTabs.forEach(([key, name]) => {
      const count = getAttributeCount(key, attributesData);
      const button = document.createElement('button');
      button.className = `tab-button whitespace-nowrap py-2 px-1 border-b-2 font-semibold text-sm transition-colors ${
        currentAttributeType === key
          ? 'border-blue-500 text-blue-600'
          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
      }`;
      button.dataset.tab = key;

      // Формируем текст с учетом настроек показа счетчиков
      const showCounters = !displaySettings || displaySettings.showCounters !== false;
      const countText = showCounters ? ` (${count})` : '';
      const shortName = name.length > 8 ? name.substring(0, 8) + '...' : name;

      // Получаем иконки статуса отображения
      const statusIcons = getDisplayStatusIcons(key);

      button.innerHTML = `
        <span class="hidden sm:inline flex items-center">
          ${name}${countText}
          ${statusIcons}
        </span>
        <span class="sm:hidden flex items-center">
          ${shortName}${countText}
          ${statusIcons}
        </span>
      `;
      mainTabsContainer.appendChild(button);
    });

    // Добавляем контейнер основных вкладок
    tabsContainer.appendChild(mainTabsContainer);

    // Создаем выпадающее меню, если есть скрытые вкладки
    if (hiddenTabs.length > 0) {
      const dropdownContainer = document.createElement('div');
      dropdownContainer.className = 'dropdown-container relative flex-shrink-0';

      const moreButton = document.createElement('button');
      moreButton.id = 'more-tabs-btn';
      moreButton.className = 'tab-button py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-semibold text-sm flex items-center transition-colors';
      moreButton.innerHTML = `
        <span class="hidden sm:inline">Ещё (${hiddenTabs.length})</span>
        <span class="sm:hidden">+${hiddenTabs.length}</span>
        <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      `;

      const dropdown = document.createElement('div');
      dropdown.id = 'more-tabs-dropdown';
      dropdown.className = 'absolute top-full right-0 mt-1 w-56 bg-white border border-gray-200 rounded-md shadow-lg hidden';

      const dropdownContent = document.createElement('div');
      dropdownContent.className = 'py-1';

      hiddenTabs.forEach(([key, name]) => {
        const count = getAttributeCount(key, attributesData);
        const statusIcons = getDisplayStatusIcons(key);
        const button = document.createElement('button');
        button.className = 'dropdown-tab-button w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex justify-between items-center transition-colors';
        button.dataset.tab = key;
        button.innerHTML = `
          <span class="flex items-center">
            ${name}
            ${statusIcons}
          </span>
          <span class="text-gray-500">(${count})</span>
        `;
        dropdownContent.appendChild(button);
      });

      dropdown.appendChild(dropdownContent);
      dropdownContainer.appendChild(moreButton);
      dropdownContainer.appendChild(dropdown);
      tabsContainer.appendChild(dropdownContainer);

      console.log('Dropdown created and added to DOM:', dropdown); // Отладка
    }

    // Переустанавливаем обработчики событий после обновления DOM
    setupTabEventListeners();
  }

  // Функция для подсчета количества элементов в каждом типе атрибута
  function getAttributeCount(attributeType, data) {
    if (!data[attributeType]) return 0;

    if (attributeType === 'standard_sizes') {
      // Для размеров считаем общее количество элементов во всех типах
      return Object.values(data[attributeType]).reduce((total, sizes) => total + sizes.length, 0);
    } else if (Array.isArray(data[attributeType])) {
      return data[attributeType].length;
    } else if (typeof data[attributeType] === 'object') {
      return Object.keys(data[attributeType]).length;
    }

    return 0;
  }

  // Инициализация
  document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 Начинаем инициализацию страницы атрибутов');

    try {
      // Сначала загружаем данные атрибутов
      console.log('📊 Загружаем данные атрибутов...');
      await loadAttributes();

      // Затем загружаем настройки отображения и применяем их
      console.log('⚙️ Загружаем настройки отображения...');
      await loadDisplaySettings();

      // Настраиваем обработчики событий
      console.log('🔧 Настраиваем обработчики событий...');
      setupEventListeners();
      setupTabEventListeners(); // Добавляем инициализацию обработчиков вкладок
      setupSettingsModal(); // Добавляем инициализацию модального окна настроек

      console.log('✅ Инициализация завершена успешно');
    } catch (error) {
      console.error('❌ Ошибка при инициализации:', error);
    }
  });

  // Настройка обработчиков событий для вкладок
  function setupTabEventListeners() {
    console.log('setupTabEventListeners called'); // Отладка
    // Удаляем старые обработчики событий для документа
    document.removeEventListener('click', handleDocumentClick);

    // Обработчики для основных вкладок
    document.querySelectorAll('.tab-button').forEach(button => {
      if (button.id !== 'more-tabs-btn') {
        // Удаляем старые обработчики
        button.removeEventListener('click', handleTabClick);
        // Добавляем новые
        button.addEventListener('click', handleTabClick);
      }
    });

    // Обработчик для кнопки "Ещё"
    const moreTabsBtn = document.getElementById('more-tabs-btn');
    console.log('More tabs button found:', moreTabsBtn); // Отладка
    if (moreTabsBtn) {
      // Удаляем старый обработчик
      moreTabsBtn.removeEventListener('click', handleMoreTabsClick);
      // Добавляем новый
      moreTabsBtn.addEventListener('click', handleMoreTabsClick);
      console.log('Event listener added to more tabs button'); // Отладка
    }

    // Обработчики для элементов выпадающего меню
    document.querySelectorAll('.dropdown-tab-button').forEach(button => {
      // Удаляем старые обработчики
      button.removeEventListener('click', handleDropdownTabClick);
      // Добавляем новые
      button.addEventListener('click', handleDropdownTabClick);
    });

    // Добавляем новый обработчик для закрытия выпадающего меню при клике вне его
    document.addEventListener('click', handleDocumentClick);

    // Добавляем обработчик для закрытия выпадающего меню при прокрутке
    window.addEventListener('scroll', closeDropdown);
    window.addEventListener('resize', closeDropdown);
  }

  // Функции для работы с модальным окном настроек
  function openSettingsModal() {
    const settingsModal = document.getElementById('settings-modal');
    if (settingsModal) {
      // Закрываем выпадающее меню вкладок, если оно открыто
      closeDropdown();

      // Заполняем форму текущими настройками
      populateSettingsForm();
      settingsModal.classList.remove('hidden');
      document.body.style.overflow = 'hidden'; // Блокируем прокрутку фона
    }
  }

  function closeSettingsModal() {
    const settingsModal = document.getElementById('settings-modal');
    if (settingsModal) {
      settingsModal.classList.add('hidden');
      document.body.style.overflow = ''; // Восстанавливаем прокрутку
    }
  }

  function populateSettingsForm() {
    // Заполняем сортируемый список типов атрибутов
    const sortableContainer = document.getElementById('sortable-tabs');
    if (sortableContainer) {
      sortableContainer.innerHTML = '';

      const orderedTypes = getOrderedAttributeTypes();
      orderedTypes.forEach(([key, name]) => {
        const item = document.createElement('div');
        item.className = 'flex items-center justify-between p-3 bg-white border border-gray-300 rounded-md cursor-move hover:bg-gray-50 hover:border-gray-400 transition-colors shadow-sm';
        item.dataset.typeKey = key;
        item.draggable = true;

        item.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
            <span class="font-semibold text-gray-900">${name}</span>
          </div>
          <span class="text-sm text-gray-600 font-medium">${getAttributeCount(key, attributesData)} элементов</span>
        `;

        sortableContainer.appendChild(item);
      });

      // Добавляем обработчики drag & drop
      setupDragAndDrop(sortableContainer);
    }

    // Заполняем остальные настройки
    const visibleTabsCount = document.getElementById('visible-tabs-count');
    if (visibleTabsCount) {
      visibleTabsCount.value = displaySettings.visibleTabsCount || 6;
    }

    const showCounters = document.getElementById('show-counters');
    if (showCounters) {
      showCounters.checked = displaySettings.showCounters !== false;
    }

    const compactMode = document.getElementById('compact-mode');
    if (compactMode) {
      compactMode.checked = displaySettings.compactMode || false;
    }
  }

  // Настройка drag & drop для сортировки типов атрибутов
  function setupDragAndDrop(container) {
    let draggedElement = null;

    container.addEventListener('dragstart', function(e) {
      draggedElement = e.target.closest('[data-type-key]');
      if (draggedElement) {
        e.dataTransfer.effectAllowed = 'move';
        draggedElement.classList.add('opacity-50');
      }
    });

    container.addEventListener('dragend', function(e) {
      if (draggedElement) {
        draggedElement.classList.remove('opacity-50');
        draggedElement = null;
      }
    });

    container.addEventListener('dragover', function(e) {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'move';
    });

    container.addEventListener('drop', function(e) {
      e.preventDefault();

      if (!draggedElement) return;

      const dropTarget = e.target.closest('[data-type-key]');
      if (dropTarget && dropTarget !== draggedElement) {
        const rect = dropTarget.getBoundingClientRect();
        const midpoint = rect.top + rect.height / 2;

        if (e.clientY < midpoint) {
          container.insertBefore(draggedElement, dropTarget);
        } else {
          container.insertBefore(draggedElement, dropTarget.nextSibling);
        }
      }
    });
  }

  // Сохранение настроек отображения
  async function saveDisplaySettings() {
    try {
      // Собираем новый порядок типов атрибутов
      const sortableContainer = document.getElementById('sortable-tabs');
      const tabOrder = [];
      if (sortableContainer) {
        const items = sortableContainer.querySelectorAll('[data-type-key]');
        items.forEach(item => {
          tabOrder.push(item.dataset.typeKey);
        });
      }

      // Собираем остальные настройки
      const visibleTabsCount = parseInt(document.getElementById('visible-tabs-count')?.value || '6');
      const showCounters = document.getElementById('show-counters')?.checked !== false;
      const compactMode = document.getElementById('compact-mode')?.checked || false;

      const newSettings = {
        tabOrder,
        visibleTabsCount,
        showCounters,
        compactMode
      };

      // Отправляем на сервер
      const response = await fetch('/api/admin/attributes-display-settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newSettings)
      });

      if (response.ok) {
        const result = await response.json();
        displaySettings = result.settings;

        // Применяем новые настройки
        applyDisplaySettings();

        // Закрываем модальное окно
        closeSettingsModal();

        // Показываем уведомление
        showNotification('Настройки успешно сохранены', 'success');
      } else {
        const error = await response.json();
        showNotification('Ошибка сохранения: ' + (error.error || 'Неизвестная ошибка'), 'error');
      }
    } catch (error) {
      console.error('Ошибка сохранения настроек:', error);
      showNotification('Ошибка сохранения настроек', 'error');
    }
  }

  // Сброс настроек к значениям по умолчанию
  async function resetDisplaySettings() {
    if (!confirm('Вы уверены, что хотите сбросить все настройки к значениям по умолчанию?')) {
      return;
    }

    try {
      const response = await fetch('/api/admin/attributes-display-settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const result = await response.json();
        displaySettings = result.settings;

        // Применяем настройки по умолчанию
        applyDisplaySettings();

        // Обновляем форму
        populateSettingsForm();

        // Показываем уведомление
        showNotification('Настройки сброшены к значениям по умолчанию', 'success');
      } else {
        const error = await response.json();
        showNotification('Ошибка сброса: ' + (error.error || 'Неизвестная ошибка'), 'error');
      }
    } catch (error) {
      console.error('Ошибка сброса настроек:', error);
      showNotification('Ошибка сброса настроек', 'error');
    }
  }

  // Обработчики событий
  function handleTabClick(e) {
    e.preventDefault();
    const tabId = this.dataset.tab;
    if (tabId) {
      switchTab(tabId);
      closeDropdown();
    }
  }

  function handleMoreTabsClick(e) {
    console.log('More tabs button clicked'); // Отладка
    e.preventDefault();
    e.stopPropagation();
    toggleDropdown();
  }

  function handleDropdownTabClick(e) {
    e.preventDefault();
    const tabId = this.dataset.tab;
    if (tabId) {
      switchTab(tabId);
      closeDropdown();
    }
  }

  // Функция для обработки кликов по документу
  function handleDocumentClick(e) {
    const dropdown = document.getElementById('more-tabs-dropdown');
    const moreBtn = document.getElementById('more-tabs-btn');

    if (dropdown && moreBtn && !dropdown.contains(e.target) && !moreBtn.contains(e.target)) {
      closeDropdown();
    }
  }

  // Функции для управления выпадающим меню
  function toggleDropdown() {
    const dropdown = document.getElementById('more-tabs-dropdown');

    console.log('toggleDropdown called', { dropdown }); // Отладка

    if (dropdown) {
      console.log('Current dropdown classes:', dropdown.className); // Отладка
      dropdown.classList.toggle('hidden');
      console.log('After toggle classes:', dropdown.className); // Отладка
    } else {
      console.log('Dropdown not found'); // Отладка
    }
  }

  function closeDropdown() {
    const dropdown = document.getElementById('more-tabs-dropdown');
    if (dropdown) {
      dropdown.classList.add('hidden');
    }
  }

  // Настройка обработчиков событий
  function setupEventListeners() {
    setupTabEventListeners();

    // Обработчики для управления типами атрибутов
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('delete-attribute-type')) {
        const attributeType = e.target.dataset.type;
        deleteAttributeType(attributeType);
      }
    });
  }



  // Настройка модального окна настроек
  function setupSettingsModal() {
    const settingsBtn = document.getElementById('settings-btn');
    const settingsModal = document.getElementById('settings-modal');
    const closeSettingsBtn = document.getElementById('close-settings-btn');
    const saveSettingsBtn = document.getElementById('save-settings-btn');
    const resetSettingsBtn = document.getElementById('reset-settings-btn');

    if (settingsBtn && settingsModal) {
      // Открытие модального окна
      settingsBtn.addEventListener('click', function(e) {
        e.preventDefault();
        openSettingsModal();
      });

      // Закрытие модального окна
      if (closeSettingsBtn) {
        closeSettingsBtn.addEventListener('click', closeSettingsModal);
      }

      // Закрытие при клике на фон
      settingsModal.addEventListener('click', function(e) {
        if (e.target === settingsModal) {
          closeSettingsModal();
        }
      });

      // Сохранение настроек
      if (saveSettingsBtn) {
        saveSettingsBtn.addEventListener('click', saveDisplaySettings);
      }

      // Сброс настроек
      if (resetSettingsBtn) {
        resetSettingsBtn.addEventListener('click', resetDisplaySettings);
      }

      // Закрытие по Escape
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !settingsModal.classList.contains('hidden')) {
          closeSettingsModal();
        }
      });
    }
  }

  // Переключение вкладок
  function switchTab(tabId) {
    // Скрыть все вкладки
    document.querySelectorAll('.tab-content').forEach(tab => {
      tab.classList.add('hidden');
    });

    // Показать выбранную вкладку или создать её, если не существует
    let targetTab = document.getElementById(`tab-${tabId}`);
    if (!targetTab) {
      // Создаем новую вкладку для пользовательского типа атрибута
      targetTab = createNewTabContent(tabId);
      document.querySelector('.container').appendChild(targetTab);
    }
    targetTab.classList.remove('hidden');

    // Обновить стили основных кнопок вкладок
    document.querySelectorAll('.tab-button').forEach(button => {
      if (button.id === 'more-tabs-btn') {
        // Проверяем, активна ли одна из вкладок в выпадающем меню
        const isDropdownTabActive = document.querySelector(`.dropdown-tab-button[data-tab="${tabId}"]`) !== null;
        if (isDropdownTabActive) {
          button.classList.remove('border-transparent', 'text-gray-500');
          button.classList.add('border-blue-500', 'text-blue-600');
        } else {
          button.classList.remove('border-blue-500', 'text-blue-600');
          button.classList.add('border-transparent', 'text-gray-500');
        }
      } else if (button.dataset.tab === tabId) {
        button.classList.remove('border-transparent', 'text-gray-500');
        button.classList.add('border-blue-500', 'text-blue-600');
      } else {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
      }
    });

    // Обновить стили элементов выпадающего меню
    document.querySelectorAll('.dropdown-tab-button').forEach(button => {
      if (button.dataset.tab === tabId) {
        button.classList.add('bg-blue-50', 'text-blue-700');
        button.classList.remove('text-gray-700');
      } else {
        button.classList.remove('bg-blue-50', 'text-blue-700');
        button.classList.add('text-gray-700');
      }
    });

    currentAttributeType = tabId;

    // Обновляем href кнопки "Добавить значение атрибута"
    const addValueButtons = document.querySelectorAll('a[href="/admin/attributes/new"]');
    addValueButtons.forEach(button => {
      button.href = `/admin/attributes/new?type=${encodeURIComponent(tabId)}`;
    });

    renderCurrentTab();
  }

  // Создание контента вкладки для пользовательского типа атрибута
  function createNewTabContent(tabId) {
    const tabContent = document.createElement('div');
    tabContent.id = `tab-${tabId}`;
    tabContent.className = 'tab-content hidden';

    const typeName = allAttributeTypes[tabId] || tabId;

    tabContent.innerHTML = `
      <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 class="text-xl font-semibold text-gray-900">${typeName}</h2>
              <p class="text-sm text-gray-500 mt-1">Управление значениями атрибута "${typeName}"</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-2">
              <a
                href="/admin/attributes/new?type=${encodeURIComponent(tabId)}"
                class="inline-flex items-center px-3 py-1.5 border border-blue-300 text-xs font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors w-full sm:w-auto justify-center"
                title="Добавить значение атрибута"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Добавить значение
              </a>
              <a
                href="/admin/attributes/edit-type/${tabId}"
                class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 transition-colors w-full sm:w-auto justify-center"
                title="Редактировать тип атрибута"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Редактировать тип
              </a>
              <button
                class="delete-attribute-type inline-flex items-center px-3 py-1.5 border border-red-300 text-xs font-medium rounded text-red-700 bg-red-50 hover:bg-red-100 transition-colors w-full sm:w-auto justify-center"
                data-type="${tabId}"
                title="Удалить тип атрибута"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Удалить тип
              </button>
            </div>
          </div>
        </div>

        <!-- Desktop Table -->
        <div class="hidden lg:block overflow-x-auto custom-scrollbar">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Значение</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="tbody-${tabId}">
              <!-- Содержимое будет загружено через JavaScript -->
            </tbody>
          </table>
        </div>

        <!-- Mobile Cards -->
        <div class="lg:hidden space-y-4 p-6" id="mobile-${tabId}">
          <!-- Содержимое будет загружено через JavaScript -->
        </div>
      </div>
    `;

    return tabContent;
  }

  // Отображение содержимого текущей вкладки
  function renderCurrentTab() {
    const tbody = document.getElementById(`tbody-${currentAttributeType}`);
    const mobileContainer = document.getElementById(`mobile-${currentAttributeType}`);

    if (!attributesData[currentAttributeType]) return;

    // Очищаем контейнеры
    if (tbody) tbody.innerHTML = '';
    if (mobileContainer) mobileContainer.innerHTML = '';

    const data = attributesData[currentAttributeType];

    if (currentAttributeType === 'sizes') {
      // Специальная обработка для размеров
      Object.entries(data).forEach(([type, sizes]) => {
        sizes.forEach((size, index) => {
          if (tbody) {
            const row = createSizeRow(type, size, index);
            tbody.appendChild(row);
          }
          if (mobileContainer) {
            const card = createSizeMobileCard(type, size, index);
            mobileContainer.appendChild(card);
          }
        });
      });
    } else if (Array.isArray(data)) {
      data.forEach(item => {
        if (tbody) {
          const row = createAttributeRow(item);
          tbody.appendChild(row);
        }
        if (mobileContainer) {
          const card = createAttributeMobileCard(item);
          mobileContainer.appendChild(card);
        }
      });
    }
  }

  // Создание строки для атрибута
  function createAttributeRow(item) {
    const row = document.createElement('tr');

    if (currentAttributeType === 'colors') {
      row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.id}</td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.name}</td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="w-6 h-6 rounded border" style="background-color: ${item.hex}"></div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.hex}</td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <div class="flex space-x-2">
            <a href="/admin/attributes/edit/${encodeURIComponent(item.id)}?type=${currentAttributeType}" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
              Редактировать
            </a>
            <button class="delete-attribute inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors" data-id="${item.id}">
              Удалить
            </button>
          </div>
        </td>
      `;
    } else if (currentAttributeType === 'textures') {
      row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item}</td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <div class="flex space-x-2">
            <a href="/admin/attributes/edit/${encodeURIComponent(item)}?type=${currentAttributeType}" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
              Редактировать
            </a>
            <button class="delete-attribute inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors" data-id="${item}">
              Удалить
            </button>
          </div>
        </td>
      `;
    } else if (['strength_classes', 'frost_resistance', 'water_absorption'].includes(currentAttributeType)) {
      row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.class}</td>
        <td class="px-6 py-4 text-sm text-gray-500">${item.description}</td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <div class="flex space-x-2">
            <a href="/admin/attributes/edit/${encodeURIComponent(item.class)}?type=${currentAttributeType}" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
              Редактировать
            </a>
            <button class="delete-attribute inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors" data-id="${item.class}">
              Удалить
            </button>
          </div>
        </td>
      `;
    } else if (['surfaces', 'patterns', 'color_pigments'].includes(currentAttributeType)) {
      row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.id}</td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.name}</td>
        <td class="px-6 py-4 text-sm text-gray-500">${item.description}</td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <div class="flex space-x-2">
            <a href="/admin/attributes/edit/${encodeURIComponent(item.id)}?type=${currentAttributeType}" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
              Редактировать
            </a>
            <button class="delete-attribute inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors" data-id="${item.id}">
              Удалить
            </button>
          </div>
        </td>
      `;
    } else {
      // Для других типов атрибутов
      if (typeof item === 'string') {
        row.innerHTML = `
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
            <div class="flex space-x-2">
              <a href="/admin/attributes/edit/${encodeURIComponent(item)}?type=${currentAttributeType}" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
                Редактировать
              </a>
              <button class="delete-attribute inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors" data-id="${item}">
                Удалить
              </button>
            </div>
          </td>
        `;
      } else if (typeof item === 'object' && item !== null) {
          const displayValue = item.name || item.id || JSON.stringify(item);
          const description = item.description ? `<br><span class="text-xs text-gray-500">${item.description}</span>` : '';
          const itemId = item.id || item.name || JSON.stringify(item);

          row.innerHTML = `
            <td class="px-6 py-4 text-sm font-medium text-gray-900">${displayValue}${description}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <a href="/admin/attributes/edit/${encodeURIComponent(itemId)}?type=${currentAttributeType}" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
                  Редактировать
                </a>
                <button class="delete-attribute inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors" data-id="${itemId}">
                  Удалить
                </button>
              </div>
            </td>
          `;
      }
    }

    // Добавляем обработчик событий для кнопки удаления
    const deleteBtn = row.querySelector('.delete-attribute');
    if (deleteBtn) {
      deleteBtn.addEventListener('click', function() {
        const id = this.dataset.id;
        deleteAttribute(id);
      });
    }

    return row;
  }

  // Создание строки для размеров
  function createSizeRow(type, size, index) {
    const row = document.createElement('tr');
    const sizeText = `${size.length}×${size.width}×${size.height} мм`;
    const sizeId = `${type}_${index}`;

    row.innerHTML = `
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${type}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${sizeText}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <div class="flex space-x-2">
          <a href="/admin/attributes/edit/${encodeURIComponent(sizeId)}?type=${currentAttributeType}" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
            Редактировать
          </a>
          <button class="delete-size inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors" data-type="${type}" data-index="${index}">
            Удалить
          </button>
        </div>
      </td>
    `;

    // Добавляем обработчик событий для удаления
    const deleteBtn = row.querySelector('.delete-size');
    if (deleteBtn) {
      deleteBtn.addEventListener('click', function() {
        deleteSize(type, index);
      });
    }

    return row;
  }

  // Создание мобильной карточки для атрибута
  function createAttributeMobileCard(item) {
    const card = document.createElement('div');
    card.className = 'mobile-card bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow';

    if (currentAttributeType === 'colors') {
      card.innerHTML = `
        <div class="flex items-start justify-between mb-3">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded border flex-shrink-0" style="background-color: ${item.hex}"></div>
            <div>
              <h3 class="text-sm font-medium text-gray-900">${item.name}</h3>
              <p class="text-xs text-gray-500 font-mono">${item.id}</p>
            </div>
          </div>
          <div class="text-xs text-gray-500 font-mono">${item.hex}</div>
        </div>
        <div class="flex space-x-2">
          <a href="/admin/attributes/edit/${encodeURIComponent(item.id)}?type=${currentAttributeType}"
             class="flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
            Редактировать
          </a>
          <button class="delete-attribute flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                  data-id="${item.id}">
            Удалить
          </button>
        </div>
      `;
    } else if (currentAttributeType === 'textures') {
      card.innerHTML = `
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-sm font-medium text-gray-900">${item}</h3>
        </div>
        <div class="flex space-x-2">
          <a href="/admin/attributes/edit/${encodeURIComponent(item)}?type=${currentAttributeType}"
             class="flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
            Редактировать
          </a>
          <button class="delete-attribute flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                  data-id="${item}">
            Удалить
          </button>
        </div>
      `;
    } else if (['strength_classes', 'frost_resistance', 'water_absorption'].includes(currentAttributeType)) {
      card.innerHTML = `
        <div class="mb-3">
          <h3 class="text-sm font-medium text-gray-900">${item.class}</h3>
          <p class="text-xs text-gray-500 mt-1">${item.description}</p>
        </div>
        <div class="flex space-x-2">
          <a href="/admin/attributes/edit/${encodeURIComponent(item.class)}?type=${currentAttributeType}"
             class="flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
            Редактировать
          </a>
          <button class="delete-attribute flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                  data-id="${item.class}">
            Удалить
          </button>
        </div>
      `;
    } else if (['surfaces', 'patterns', 'color_pigments'].includes(currentAttributeType)) {
      card.innerHTML = `
        <div class="flex items-start justify-between mb-3">
          <div>
            <h3 class="text-sm font-medium text-gray-900">${item.name}</h3>
            <p class="text-xs text-gray-500 font-mono">${item.id}</p>
            <p class="text-xs text-gray-500 mt-1">${item.description}</p>
          </div>
        </div>
        <div class="flex space-x-2">
          <a href="/admin/attributes/edit/${encodeURIComponent(item.id)}?type=${currentAttributeType}"
             class="flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
            Редактировать
          </a>
          <button class="delete-attribute flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                  data-id="${item.id}">
            Удалить
          </button>
        </div>
      `;
    } else {
      // Для других типов атрибутов
      if (typeof item === 'string') {
        card.innerHTML = `
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-sm font-medium text-gray-900">${item}</h3>
          </div>
          <div class="flex space-x-2">
            <a href="/admin/attributes/edit/${encodeURIComponent(item)}?type=${currentAttributeType}"
               class="flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
              Редактировать
            </a>
            <button class="delete-attribute flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                    data-id="${item}">
              Удалить
            </button>
          </div>
        `;
      } else if (typeof item === 'object' && item !== null) {
        const displayValue = item.name || item.id || JSON.stringify(item);
        const description = item.description ? `<p class="text-xs text-gray-500 mt-1">${item.description}</p>` : '';
        const itemId = item.id || item.name || JSON.stringify(item);

        card.innerHTML = `
          <div class="mb-3">
            <h3 class="text-sm font-medium text-gray-900">${displayValue}</h3>
            ${description}
          </div>
          <div class="flex space-x-2">
            <a href="/admin/attributes/edit/${encodeURIComponent(itemId)}?type=${currentAttributeType}"
               class="flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
              Редактировать
            </a>
            <button class="delete-attribute flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                    data-id="${itemId}">
              Удалить
            </button>
          </div>
        `;
      }
    }

    // Добавляем обработчик событий для кнопки удаления
    const deleteBtn = card.querySelector('.delete-attribute');
    if (deleteBtn) {
      deleteBtn.addEventListener('click', function() {
        const id = this.dataset.id;
        deleteAttribute(id);
      });
    }

    return card;
  }

  // Создание мобильной карточки для размеров
  function createSizeMobileCard(type, size, index) {
    const card = document.createElement('div');
    card.className = 'mobile-card bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow';

    const sizeText = `${size.length}×${size.width}×${size.height} мм`;
    const sizeId = `${type}_${index}`;

    card.innerHTML = `
      <div class="flex items-start justify-between mb-3">
        <div>
          <h3 class="text-sm font-medium text-gray-900">${type}</h3>
          <p class="text-xs text-gray-500 mt-1">${sizeText}</p>
        </div>
      </div>
      <div class="flex space-x-2">
        <a href="/admin/attributes/edit/${encodeURIComponent(sizeId)}?type=${currentAttributeType}"
           class="flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
          Редактировать
        </a>
        <button class="delete-size flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                data-type="${type}" data-index="${index}">
          Удалить
        </button>
      </div>
    `;

    // Добавляем обработчик событий для удаления
    const deleteBtn = card.querySelector('.delete-size');
    if (deleteBtn) {
      deleteBtn.addEventListener('click', function() {
        deleteSize(type, index);
      });
    }

    return card;
  }

  // Функции удаления
  async function deleteAttribute(id) {
    const confirmed = await window.confirmModal?.show({
      title: 'Подтверждение удаления',
      message: 'Вы уверены, что хотите удалить этот атрибут?',
      confirmText: 'Удалить',
      cancelText: 'Отмена',
      confirmButtonClass: 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300'
    });

    if (confirmed) {
      try {
        const response = await fetch('/api/admin/attributes', {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ attributeType: currentAttributeType, id })
        });

        if (response.ok) {
          await loadAttributes();
          await window.adminModal?.showSuccess('Атрибут успешно удален');
        } else {
          const error = await response.json();
          await window.adminModal?.showError('Ошибка при удалении: ' + (error.error || 'Неизвестная ошибка'));
        }
      } catch (error) {
        console.error('Ошибка:', error);
        await window.adminModal?.showError('Ошибка при удалении атрибута');
      }
    }
  }

  async function deleteSize(type, index) {
    if (confirm('Вы уверены, что хотите удалить этот размер?')) {
      try {
        const response = await fetch('/api/admin/attributes', {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            attributeType: 'standard_sizes',
            sizeType: type,
            sizeIndex: index
          })
        });

        if (response.ok) {
          await loadAttributes();
          await window.adminModal?.showSuccess('Размер успешно удален');
        } else {
          const error = await response.json();
          await window.adminModal?.showError('Ошибка при удалении: ' + (error.error || 'Неизвестная ошибка'));
        }
      } catch (error) {
        console.error('Ошибка:', error);
        await window.adminModal?.showError('Ошибка при удалении размера');
      }
    }
  }

  async function deleteAttributeType(attributeType) {
    // Получаем название типа из конфигурации или генерируем из ключа
    const typeName = (attributeTypesConfig[attributeType] && attributeTypesConfig[attributeType].name) ||
                     attributeType.replace(/_/g, ' ').replace(/^./, l => l.toUpperCase());

    const attributeCount = attributesData[attributeType] ?
      (Array.isArray(attributesData[attributeType]) ? attributesData[attributeType].length : Object.keys(attributesData[attributeType]).length) : 0;

    const confirmMessage = attributeCount > 0
      ? `Вы уверены, что хотите удалить тип атрибута "${typeName}" и все его ${attributeCount} значений? Это действие нельзя отменить.`
      : `Вы уверены, что хотите удалить тип атрибута "${typeName}"?`;

    const confirmed = await window.confirmModal?.show({
      title: 'Подтверждение удаления',
      message: confirmMessage,
      confirmText: 'Удалить',
      cancelText: 'Отмена',
      confirmButtonClass: 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300'
    });

    if (confirmed) {
      try {
        const response = await fetch('/api/admin/attribute-types-config', {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ typeKey: attributeType })
        });

        if (response.ok) {
          await loadAttributes();
          // Переключаемся на первую вкладку
          switchTab('colors');
          await window.adminModal?.showSuccess('Тип атрибута успешно удален');
          // Перезагружаем страницу для обновления вкладок
          window.location.reload();
        } else {
          const error = await response.json();
          await window.adminModal?.showError('Ошибка при удалении: ' + (error.error || 'Неизвестная ошибка'));
        }
      } catch (error) {
        console.error('Ошибка:', error);
        await window.adminModal?.showError('Ошибка при удалении типа атрибута');
      }
    }
  }



  // Функции для показа сообщений
  function showSuccessMessage(message) {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-md shadow-lg z-50';
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
  }

  function showErrorMessage(message) {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-md shadow-lg z-50';
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
  }

  // Универсальная функция для показа уведомлений
  function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    const icon = document.getElementById('notification-icon');
    const messageEl = document.getElementById('notification-message');

    if (!notification || !icon || !messageEl) {
      // Fallback к старым функциям если элементы не найдены
      if (type === 'success') {
        showSuccessMessage(message);
      } else if (type === 'error') {
        showErrorMessage(message);
      }
      return;
    }

    messageEl.textContent = message;

    // Устанавливаем иконку в зависимости от типа
    if (type === 'success') {
      icon.innerHTML = '<svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
    } else if (type === 'error') {
      icon.innerHTML = '<svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
    } else {
      icon.innerHTML = '<svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
    }

    notification.classList.remove('hidden');

    // Автоматически скрываем через 5 секунд
    setTimeout(() => {
      notification.classList.add('hidden');
    }, 5000);
  }
</script>
