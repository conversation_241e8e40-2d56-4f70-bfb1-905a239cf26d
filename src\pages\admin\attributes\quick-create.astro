---
import AdminLayout from '../../../layouts/AdminLayout.astro';
---

<AdminLayout title="Быстрое создание типа атрибута">
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
      <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold mb-6">Быстрое создание типа атрибута</h1>
        
        <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 class="font-semibold text-blue-800 mb-2">Автоматическое определение типа</h3>
          <p class="text-blue-700 text-sm">
            Система автоматически определит тип атрибута и создаст подходящую конфигурацию на основе названия:
          </p>
          <ul class="text-blue-700 text-sm mt-2 list-disc list-inside">
            <li><strong>Размер/Габариты</strong> → Поля: длина, ширина, высота</li>
            <li><strong>Вес/Масса</strong> → Поля: значение, единица измерения</li>
            <li><strong>Материал</strong> → Поля: ID, название, описание</li>
            <li><strong>Цвет</strong> → Поля: ID, название, hex-код</li>
            <li><strong>Класс/Категория/Тип</strong> → Поля: ID, название, описание</li>
            <li><strong>Другое</strong> → Простой текстовый атрибут</li>
          </ul>
        </div>

        <form id="quickCreateForm" class="space-y-6">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
              Название типа атрибута *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              required
              placeholder="Например: Материал корпуса, Размер упаковки, Класс прочности"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p class="text-sm text-gray-500 mt-1">
              Ключ будет сгенерирован автоматически на основе названия
            </p>
          </div>

          <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
              Описание (необязательно)
            </label>
            <textarea
              id="description"
              name="description"
              rows="3"
              placeholder="Краткое описание назначения этого типа атрибута"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            ></textarea>
          </div>

          <div class="flex gap-4">
            <button
              type="submit"
              class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Создать тип атрибута
            </button>
            <a
              href="/admin/attributes"
              class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 text-center"
            >
              Отмена
            </a>
          </div>
        </form>

        <div id="result" class="mt-6 hidden"></div>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  document.getElementById('quickCreateForm')?.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target as HTMLFormElement);
    const name = formData.get('name') as string;
    const description = formData.get('description') as string;
    
    if (!name.trim()) {
      showResult('Пожалуйста, введите название типа атрибута', 'error');
      return;
    }

    try {
      const response = await fetch('/api/admin/attribute-types-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          createWithDefaults: true,
          name: name.trim(),
          description: description.trim()
        })
      });

      const result = await response.json();

      if (response.ok) {
        showResult(
          `✅ ${result.message}<br><br>
          <strong>Сгенерированный ключ:</strong> ${result.typeKey}<br>
          <strong>Тип:</strong> ${getDetectedType(name)}<br><br>
          <a href="/admin/attributes" class="text-blue-600 hover:underline">← Вернуться к управлению атрибутами</a>`,
          'success'
        );
        
        // Очищаем форму
        (e.target as HTMLFormElement).reset();
      } else {
        showResult(`❌ Ошибка: ${result.error}`, 'error');
      }
    } catch (error) {
      showResult(`❌ Ошибка сети: ${error.message}`, 'error');
    }
  });

  function showResult(message: string, type: 'success' | 'error') {
    const resultDiv = document.getElementById('result');
    if (!resultDiv) return;

    resultDiv.className = `mt-6 p-4 rounded-lg ${
      type === 'success' 
        ? 'bg-green-50 border border-green-200 text-green-800' 
        : 'bg-red-50 border border-red-200 text-red-800'
    }`;
    resultDiv.innerHTML = message;
    resultDiv.classList.remove('hidden');
  }

  function getDetectedType(name: string): string {
    const lowerName = name.toLowerCase();
    
    if (lowerName.includes('размер') || lowerName.includes('габарит')) {
      return 'Размеры (длина × ширина × высота)';
    } else if (lowerName.includes('вес') || lowerName.includes('масса')) {
      return 'Вес (значение + единица измерения)';
    } else if (lowerName.includes('цвет')) {
      return 'Цвет (ID + название + hex-код)';
    } else if (lowerName.includes('материал')) {
      return 'Материал (ID + название + описание)';
    } else if (lowerName.includes('класс') || lowerName.includes('категория') || lowerName.includes('тип')) {
      return 'Классификация (ID + название + описание)';
    } else {
      return 'Простой текстовый атрибут';
    }
  }
</script>
